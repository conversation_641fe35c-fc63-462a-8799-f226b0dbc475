---
section: cli-commands 
title: npm-install-ci-test
description: Install a project with a clean slate and run tests
---

# npm install-ci-test(1)

## Install a project with a clean slate and run tests

### Synopsis

```bash
npm install-ci-test

alias: npm cit
```

### Description

This command runs an `npm ci` followed immediately by an `npm test`.

### See Also

* [npm ci](/cli-commands/npm-ci)
* [npm test](/cli-commands/npm-test)
