# Visual Anchor Embeddings for ControlMLLM

基于ATPrompt启发的test-time text embedding优化方法，通过多层次可学习anchor embeddings实现更精确的visual attention控制。

## 🎯 核心思想

传统方法直接优化整个text prompt存在不稳定和语义破坏的问题。我们提出**Visual Anchor Embeddings**方法：

1. **在原始问题前添加可学习的anchor embeddings**
2. **分层次设计anchors**: spatial (空间) → semantic (语义) → relational (关系)  
3. **通过attention loss进行test-time优化**

```
[spatial_anchor1] [spatial_anchor2] [semantic_anchor1] [semantic_anchor2] 
[relational_anchor1] [relational_anchor2] {original_question}
```

## 🏗️ 方法架构

### Multi-level Anchor Design

```python
class VisualAnchorOptimizer(nn.Module):
    def __init__(self):
        # Level 1: Spatial Anchors - 学习空间定位和区域关注
        self.spatial_anchors = nn.Parameter(torch.randn(n_spatial, embed_dim))
        
        # Level 2: Semantic Anchors - 学习语义特征理解  
        self.semantic_anchors = nn.Parameter(torch.randn(n_semantic, embed_dim))
        
        # Level 3: Relational Anchors - 学习对象关系比较
        self.relational_anchors = nn.Parameter(torch.randn(n_relational, embed_dim))
```

### 优化流程

1. **初始化**: 为每个问题创建随机初始化的anchor embeddings
2. **嵌入**: 将anchors插入到原始text embeddings前面
3. **前向**: 计算LLaVA输出和attention maps
4. **Loss计算**: 使用cross-attention loss衡量attention与visual prompt的一致性
5. **反向**: 只对anchor embeddings进行梯度更新
6. **迭代**: 重复T步直到收敛

## 🚀 快速开始

### 1. 运行基本实验

```bash
# 给脚本添加执行权限
chmod +x run_visual_anchors.sh

# 运行实验
./run_visual_anchors.sh
```

### 2. 自定义配置

编辑 `config_visual_anchors.yaml` 调整参数：

```yaml
# 核心anchor配置
visual_anchors:
  spatial:
    count: 2               # 空间anchor数量
  semantic:
    count: 2               # 语义anchor数量  
  relational:
    count: 2               # 关系anchor数量

# 优化参数
optimization:
  T: 15                    # 优化步数
  lr: 0.02                 # 学习率
  alpha: 400               # loss权重
```

### 3. 分析结果

```bash
# 运行分析脚本
python analyze_visual_anchors.py --results_dir outputs --output_dir analysis
```

## 📊 实验设置

### 推荐配置

| Visual Prompt | Spatial | Semantic | Relational | Learning Rate | Steps |
|---------------|---------|----------|------------|---------------|-------|
| Box           | 2       | 2        | 2          | 0.02          | 15    |
| Mask          | 3       | 1        | 2          | 0.015         | 20    |
| Point         | 3       | 2        | 1          | 0.025         | 12    |
| Scribble      | 2       | 3        | 1          | 0.02          | 18    |

### 消融实验

自动运行不同anchor配置的对比实验：

- **spatial_only**: 只使用空间anchors
- **semantic_only**: 只使用语义anchors  
- **relational_only**: 只使用关系anchors
- **spatial_semantic**: 空间+语义anchors
- **full_config**: 完整三层anchors

## 🔬 技术创新点

### 1. 层次化Anchor设计

不同类型的anchors学习不同层面的visual grounding能力：

- **Spatial Anchors**: 空间定位，学习"在哪里看"
- **Semantic Anchors**: 语义理解，学习"看什么特征"  
- **Relational Anchors**: 关系比较，学习"如何比较"

### 2. 关系感知优化

根据问题中对象的关系类型动态调整优化策略：

```python
def analyze_object_relationship(opt1, opt2):
    # 检测containment关系: person vs necktie
    # 检测similarity关系: carrot vs broccoli  
    # 检测unrelated关系: basket vs knife
    return relationship_type
```

### 3. 稳定的优化策略

- **EMA更新**: 指数移动平均提升稳定性
- **Early stopping**: 避免过拟合
- **最佳状态保存**: 记录最优anchor配置

## 📈 预期效果

### 相比原方法的优势

1. **更稳定**: 只优化少量anchor参数，避免破坏原始语义
2. **更通用**: anchors学到的是可复用的visual grounding模式
3. **更可控**: 可以分析每种anchor的具体贡献
4. **更高效**: 参数量少，优化速度快

### 性能提升预期

- **Attention Focus**: 10-20% 提升attention map与visual prompt一致性
- **Spatial Grounding**: 15-25% 改善空间定位精度  
- **Semantic Understanding**: 8-15% 增强目标对象识别
- **Relational Reasoning**: 12-18% 改进多对象比较

## 🔧 参数调优指南

### 学习率选择

```python
# 不同visual prompt类型的建议学习率
LEARNING_RATES = {
    'Box': 0.02,        # 空间边界明确，可用较大学习率
    'Mask': 0.015,      # 形状复杂，需要较小学习率
    'Point': 0.025,     # 单点定位，可用最大学习率
    'Scribble': 0.02    # 中等复杂度
}
```

### Anchor数量优化

```python
# 总anchor数量建议: 4-8个
# 三类anchor比例建议:
- 简单场景: spatial=2, semantic=1, relational=1
- 复杂场景: spatial=2, semantic=2, relational=2  
- 关系密集: spatial=1, semantic=2, relational=3
```

## 📁 文件结构

```
ControlMLLM/
├── controlmllm/task/ROC/
│   └── llava_roc_visual_anchors.py     # 核心实现
├── run_visual_anchors.sh               # 运行脚本
├── config_visual_anchors.yaml          # 配置文件
├── analyze_visual_anchors.py           # 结果分析
└── Visual_Anchor_Embeddings_README.md # 说明文档
```

## 🎛️ 高级用法

### 1. 自定义Anchor类型

```python
# 添加新的anchor类型
class ExtendedVisualAnchorOptimizer(VisualAnchorOptimizer):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 添加temporal anchors用于视频理解
        self.temporal_anchors = nn.Parameter(torch.randn(n_temporal, embed_dim))
```

### 2. 条件化Anchor初始化

```python
# 根据问题类型初始化anchors
def initialize_anchors_by_question_type(question_text):
    if "color" in question_text:
        # 增强semantic anchors
        semantic_weight = 1.5
    elif "position" in question_text:
        # 增强spatial anchors  
        spatial_weight = 1.5
```

### 3. 注意力图可视化

```bash
# 启用attention map可视化
python llava_roc_visual_anchors.py --show_att --verbose
```

## 🔮 未来扩展

1. **多模态Anchors**: 结合视觉和文本特征的hybrid anchors
2. **动态Anchor选择**: 根据问题内容自动选择最佳anchor配置
3. **跨任务迁移**: 将学到的anchors迁移到其他vision-language任务
4. **在线学习**: 实时更新anchor库以适应新的视觉场景

## 📚 理论基础

基于以下理论支撑：

1. **Prompt Learning Theory**: 可学习prompt在NLP中的成功应用
2. **Attention Mechanism**: Transformer中attention的可解释性研究  
3. **Visual Grounding**: 视觉-语言对齐的最新进展
4. **Meta-Learning**: 快速适应新任务的学习范式

## 🤝 贡献

欢迎提交Issues和Pull Requests来改进这个方法！

---

**作者**: ControlMLLM Team  
**灵感来源**: [ATPrompt: Advancing Textual Prompt Learning with Anchored Attributes](https://arxiv.org/abs/2412.09442)  
**最后更新**: 2024年12月 