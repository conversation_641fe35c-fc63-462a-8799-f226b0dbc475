# Visual Anchor Embeddings Configuration
# 基于ATPrompt启发的ControlMLLM优化配置

# =============================================================================
# 模型和数据路径配置
# =============================================================================
model:
  model_path: "pretrained_models/llava-1.5-7b-hf"
  
data:
  data_path: "data/ROC/LVIS"
  question_file: "data/ROC/question_roc.json"
  output_dir: "outputs"

# =============================================================================
# Visual Prompt设置
# =============================================================================
visual_prompt:
  type: "Box"  # 选项: Box, Mask, Scribble, Point
  H: 24
  W: 24 
  n_px: 224

# =============================================================================
# 核心优化参数
# =============================================================================
optimization:
  T: 15                    # 优化步数 (推荐: 10-20)
  alpha: 400               # attention loss权重 (推荐: 200-600)
  lr: 0.02                 # 学习率 (推荐: 0.01-0.05)
  beta: 0.7                # EMA权重 (推荐: 0.5-0.9)

# =============================================================================
# Visual Anchor配置 - 核心创新
# =============================================================================
visual_anchors:
  # 空间锚点 - 学习空间定位和区域关注
  spatial:
    count: 2               # 空间anchor数量 (推荐: 1-3)
    description: "学习目标对象的空间位置和区域边界"
    
  # 语义锚点 - 学习语义特征理解
  semantic:
    count: 2               # 语义anchor数量 (推荐: 1-3)  
    description: "学习对象的语义特征和视觉属性"
    
  # 关系锚点 - 学习对象关系比较
  relational:
    count: 2               # 关系anchor数量 (推荐: 1-3)
    description: "学习对象间的关系和比较逻辑"
    
  # 初始化参数
  init_std: 0.01           # anchor初始化标准差 (推荐: 0.005-0.02)

# =============================================================================
# 高级优化策略
# =============================================================================
advanced:
  use_ema: true            # 使用指数移动平均
  early_stop: true         # 启用早停机制
  loss_threshold: 0.001    # 早停loss阈值
  verbose: true            # 详细输出
  show_attention: false    # 显示注意力图 (调试用)

# =============================================================================
# 实验配置
# =============================================================================
experiments:
  # 消融实验设置
  ablation:
    enabled: true
    configs:
      - name: "spatial_only"
        spatial: 3
        semantic: 0
        relational: 0
        
      - name: "semantic_only"
        spatial: 0
        semantic: 3
        relational: 0
        
      - name: "relational_only"
        spatial: 0
        semantic: 0
        relational: 3
        
      - name: "spatial_semantic"
        spatial: 2
        semantic: 2
        relational: 0
        
      - name: "full_config"
        spatial: 2
        semantic: 2
        relational: 2

# =============================================================================
# 方法说明和最佳实践
# =============================================================================
methodology:
  description: |
    Visual Anchor Embeddings方法通过在原始text prompt前添加可学习的
    anchor embeddings来实现test-time优化。这些anchors分为三个层次:
    
    1. Spatial Anchors: 学习空间定位，帮助模型关注正确的图像区域
    2. Semantic Anchors: 学习语义理解，提升对目标对象的识别
    3. Relational Anchors: 学习关系比较，改善多对象场景的处理
    
    相比直接优化text embeddings，这种方法:
    - 更稳定: 只优化少量参数，避免破坏原始语义
    - 更通用: anchor学到的是可复用的visual grounding能力  
    - 更可控: 可以单独分析每种anchor的贡献
    
  best_practices:
    - 优化步数T=10-20通常足够，过多可能过拟合
    - 学习率0.01-0.05较为稳定，可根据loss收敛情况调整
    - anchor数量不宜过多，总数6-9个比较合适
    - 启用EMA和早停可以提升稳定性
    - 不同visual prompt类型可能需要不同的anchor配置

# =============================================================================
# 预期改进效果
# =============================================================================
expected_improvements:
  attention_focus: "提升attention map与visual prompt的一致性"
  spatial_grounding: "改善空间定位精度，特别是Box和Point类型"
  semantic_understanding: "增强对目标对象的语义理解"
  relational_reasoning: "改进多对象比较和关系推理"
  generalization: "在不同类型问题上的泛化能力" 