#!/usr/bin/env python3
"""
Visual Anchor Embeddings 实验结果分析脚本
分析不同anchor配置的效果，生成可视化报告
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
import argparse
import os
from pathlib import Path

def parse_args():
    parser = argparse.ArgumentParser(description="Analyze Visual Anchor Embeddings Results")
    parser.add_argument('--results_dir', type=str, default='outputs', 
                       help='Directory containing result JSON files')
    parser.add_argument('--output_dir', type=str, default='analysis', 
                       help='Directory to save analysis results')
    parser.add_argument('--baseline_file', type=str, default=None,
                       help='Baseline results file for comparison')
    return parser.parse_args()

def load_results(file_path):
    """加载实验结果"""
    results = []
    with open(file_path, 'r') as f:
        for line in f:
            if line.strip():
                results.append(json.loads(line))
    return results

def extract_config_from_filename(filename):
    """从文件名提取配置信息"""
    config = {}
    parts = filename.replace('.json', '').split('_')
    
    for i, part in enumerate(parts):
        if part.startswith('T') and part[1:].isdigit():
            config['T'] = int(part[1:])
        elif part.startswith('lr'):
            try:
                config['lr'] = float(part[2:])
            except:
                pass
    
    if 'spatial_only' in filename:
        config['anchor_type'] = 'spatial_only'
    elif 'semantic_only' in filename:
        config['anchor_type'] = 'semantic_only'
    elif 'relational_only' in filename:
        config['anchor_type'] = 'relational_only'
    else:
        config['anchor_type'] = 'full'
    
    return config

def analyze_single_experiment(results):
    """分析单个实验的结果"""
    analysis = {
        'total_questions': len(results),
        'avg_loss': np.mean([r['final_loss'] for r in results if 'final_loss' in r]),
        'loss_std': np.std([r['final_loss'] for r in results if 'final_loss' in r]),
        'relationship_stats': Counter([r.get('relationship_type', 'unknown') for r in results])
    }
    
    # 按关系类型分析loss
    loss_by_relationship = defaultdict(list)
    for r in results:
        if 'final_loss' in r and 'relationship_type' in r:
            loss_by_relationship[r['relationship_type']].append(r['final_loss'])
    
    analysis['loss_by_relationship'] = {
        rel: {
            'avg': np.mean(losses), 
            'std': np.std(losses),
            'count': len(losses)
        } 
        for rel, losses in loss_by_relationship.items()
    }
    
    return analysis

def compare_experiments(experiments):
    """比较多个实验的结果"""
    comparison = {}
    
    # 总体性能比较
    for name, (results, config) in experiments.items():
        analysis = analyze_single_experiment(results)
        comparison[name] = {
            'config': config,
            'performance': analysis
        }
    
    return comparison

def create_visualizations(comparison, output_dir):
    """创建可视化图表"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. Loss比较条形图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    names = list(comparison.keys())
    avg_losses = [comparison[name]['performance']['avg_loss'] for name in names]
    loss_stds = [comparison[name]['performance']['loss_std'] for name in names]
    
    ax1.bar(names, avg_losses, yerr=loss_stds, capsize=5, alpha=0.7)
    ax1.set_title('Average Loss by Anchor Configuration')
    ax1.set_ylabel('Average Loss')
    ax1.tick_params(axis='x', rotation=45)
    
    # 2. 不同关系类型的性能
    relationship_types = set()
    for name in names:
        relationship_types.update(comparison[name]['performance']['loss_by_relationship'].keys())
    
    relationship_data = defaultdict(list)
    config_names = []
    
    for name in names:
        config_names.append(name)
        for rel_type in sorted(relationship_types):
            if rel_type in comparison[name]['performance']['loss_by_relationship']:
                relationship_data[rel_type].append(
                    comparison[name]['performance']['loss_by_relationship'][rel_type]['avg']
                )
            else:
                relationship_data[rel_type].append(0)
    
    x = np.arange(len(config_names))
    width = 0.2
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    
    for i, (rel_type, values) in enumerate(sorted(relationship_data.items())):
        ax2.bar(x + i * width, values, width, label=rel_type, 
               color=colors[i % len(colors)], alpha=0.7)
    
    ax2.set_title('Loss by Relationship Type and Configuration')
    ax2.set_ylabel('Average Loss')
    ax2.set_xlabel('Configuration')
    ax2.set_xticks(x + width * 1.5)
    ax2.set_xticklabels(config_names, rotation=45)
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'performance_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 关系类型分布热力图
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 构建热力图数据
    heatmap_data = []
    for name in names:
        row = []
        for rel_type in sorted(relationship_types):
            count = comparison[name]['performance']['relationship_stats'].get(rel_type, 0)
            row.append(count)
        heatmap_data.append(row)
    
    sns.heatmap(heatmap_data, 
                xticklabels=sorted(relationship_types),
                yticklabels=names,
                annot=True, fmt='d', cmap='Blues',
                ax=ax)
    ax.set_title('Question Count by Relationship Type and Configuration')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'relationship_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()

def generate_report(comparison, output_dir):
    """生成分析报告"""
    report_path = os.path.join(output_dir, 'analysis_report.md')
    
    with open(report_path, 'w') as f:
        f.write("# Visual Anchor Embeddings Analysis Report\n\n")
        
        f.write("## Executive Summary\n\n")
        
        # 找到最佳配置
        best_config = min(comparison.keys(), 
                         key=lambda x: comparison[x]['performance']['avg_loss'])
        best_loss = comparison[best_config]['performance']['avg_loss']
        
        f.write(f"**Best Configuration**: {best_config}\n")
        f.write(f"**Best Average Loss**: {best_loss:.4f}\n\n")
        
        f.write("## Detailed Results\n\n")
        
        for name, data in comparison.items():
            f.write(f"### {name}\n\n")
            perf = data['performance']
            
            f.write(f"- **Average Loss**: {perf['avg_loss']:.4f} ± {perf['loss_std']:.4f}\n")
            f.write(f"- **Total Questions**: {perf['total_questions']}\n")
            
            f.write(f"\n**Performance by Relationship Type**:\n")
            for rel_type, stats in perf['loss_by_relationship'].items():
                f.write(f"- {rel_type}: {stats['avg']:.4f} ± {stats['std']:.4f} ({stats['count']} questions)\n")
            
            f.write("\n")
        
        f.write("## Key Insights\n\n")
        
        # 计算相对改进
        if len(comparison) > 1:
            baseline_loss = max(comparison[name]['performance']['avg_loss'] for name in comparison)
            improvement = (baseline_loss - best_loss) / baseline_loss * 100
            f.write(f"- Best configuration achieves {improvement:.1f}% improvement over baseline\n")
        
        # 按关系类型分析
        f.write("- **Relationship Type Analysis**:\n")
        all_relationship_performance = defaultdict(list)
        for name, data in comparison.items():
            for rel_type, stats in data['performance']['loss_by_relationship'].items():
                all_relationship_performance[rel_type].append(stats['avg'])
        
        for rel_type, losses in all_relationship_performance.items():
            avg_loss = np.mean(losses)
            std_loss = np.std(losses)
            f.write(f"  - {rel_type}: Average loss {avg_loss:.4f} ± {std_loss:.4f}\n")
        
        f.write("\n## Recommendations\n\n")
        f.write("1. **Best Overall Configuration**: Use the configuration that achieved lowest average loss\n")
        f.write("2. **Relationship-Specific Optimization**: Consider different anchor configurations for different relationship types\n")
        f.write("3. **Further Investigation**: Analyze attention maps for the best performing configurations\n")

def main():
    args = parse_args()
    
    # 查找所有结果文件
    results_dir = Path(args.results_dir)
    result_files = list(results_dir.glob('*visual_anchors*.json'))
    
    if not result_files:
        print(f"No visual anchor results found in {args.results_dir}")
        return
    
    print(f"Found {len(result_files)} result files")
    
    # 加载和分析所有实验
    experiments = {}
    for file_path in result_files:
        config = extract_config_from_filename(file_path.name)
        results = load_results(file_path)
        
        # 使用文件名作为实验名称 (简化)
        exp_name = file_path.stem.replace('llava_roc_', '')
        experiments[exp_name] = (results, config)
        
        print(f"Loaded {exp_name}: {len(results)} questions")
    
    # 比较实验
    comparison = compare_experiments(experiments)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 生成可视化
    create_visualizations(comparison, args.output_dir)
    
    # 生成报告
    generate_report(comparison, args.output_dir)
    
    # 打印简要总结
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY")
    print("="*50)
    
    for name, data in comparison.items():
        perf = data['performance']
        print(f"{name:20s}: Loss = {perf['avg_loss']:.4f} ± {perf['loss_std']:.4f}")
    
    best_config = min(comparison.keys(), 
                     key=lambda x: comparison[x]['performance']['avg_loss'])
    print(f"\nBest Configuration: {best_config}")
    
    print(f"\nAnalysis results saved to: {args.output_dir}")
    print("- performance_comparison.png")
    print("- relationship_distribution.png") 
    print("- analysis_report.md")

if __name__ == "__main__":
    main() 