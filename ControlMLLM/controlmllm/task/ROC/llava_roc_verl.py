#!/usr/bin/env python3
"""
Test-time GRPO ControlMLLM using VERL framework
VERL's GRPO already includes entropy loss functionality
"""
import sys,os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from functools import partial
import logging
from peft import LoraConfig, get_peft_model, TaskType
import copy
from transformers.models.llama.modeling_llama import repeat_kv, apply_rotary_pos_emb, eager_attention_forward, ALL_ATTENTION_FUNCTIONS
import math
import torch.nn as nn
from transformers.models.llama.modeling_llama import LlamaAttention
import torch.nn.functional as F
from tqdm import tqdm
import cv2
import json
import numpy as np
import torchvision.transforms as transforms
from utils import compute_ca_loss, show_image_relevance
from transformers import AutoProcessor, LlavaForConditionalGeneration, BitsAndBytesConfig, AutoTokenizer, AutoModelForCausalLM
import gc
import torch
from PIL import Image
import argparse
from visualizer import get_local
import os
import sys
import clip
sys.setrecursionlimit(20000)
sys.path.insert(0, os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..")))
get_local.activate()
_, preprocess = clip.load("ViT-B/32", device='cpu', jit=False)
# VERL imports
try:
    from verl import DataProto
    from verl.trainer.main_ppo import run_ppo
    from verl.trainer.ppo.ray_trainer import RayPPOTrainer
    from verl.trainer.ppo.reward import load_reward_manager
    from verl.experimental.dataset.sampler import AbstractSampler
    from verl.utils.reward_score import default_compute_score
    from verl.trainer.config.algorithm import AlgoConfig
except ImportError as e:
    print(f"Warning: Could not import VERL components: {e}")
    print("Please install VERL: pip install verl")
    sys.exit(1)


def parse_args():
    parser = argparse.ArgumentParser(
        description="Test-time GRPO ControlMLLM using VERL")

    # Model paths
    parser.add_argument('--model_path', type=str,
                        default="pretrained_models/llava-1.5-7b-hf", help='Path to the pretrained model')
    parser.add_argument('--policy_model_path', type=str,
                        default="Qwen/Qwen2.5-4B-Instruct", help='Path to the policy model')
    parser.add_argument('--data_path', type=str,
                        default="data/ROC/LVIS", help='Path to the dataset')
    parser.add_argument('--question_file', type=str,
                        default='data/ROC/question_roc.json', help='Path to the question file')
    parser.add_argument('--answers_file', type=str,
                        default='outputs/llava_roc_verl.json', help='Path to the answers file')

    # Parameters
    parser.add_argument('--visual_prompt', type=str,
                        default='Box', help='Visual prompt method')
    parser.add_argument('--H', type=int, default=24,
                        help='Height of the attention map')
    parser.add_argument('--W', type=int, default=24,
                        help='Width of the attention map')
    parser.add_argument('--n_px', type=int, default=224,
                        help='Input size of image')
    parser.add_argument('--alpha', type=float, default=400,
                        help='Alpha parameter')
    parser.add_argument('--T', type=int, default=5,
                        help='T parameter for test-time GRPO')
    parser.add_argument('--lr', type=float, default=1e-6,
                        help='Learning rate for policy model')
    parser.add_argument('--epsilon', type=float, default=0.2,
                        help='GRPO epsilon parameter')
    parser.add_argument('--num_generations', type=int,
                        default=4, help='Number of generations per prompt')
    parser.add_argument('--max_completion_length', type=int,
                        default=30, help='Maximum completion length')
    parser.add_argument('--resume', action='store_true',
                        help='Resume from checkpoint')
    parser.add_argument('--enable_thinking', action='store_true',
                        help='Enable Qwen3 thinking mode in prompt')
    parser.add_argument('--show_att', action='store_true',
                        help='Flag to show attention maps')

    # VERL specific parameters
    parser.add_argument('--entropy_weight', type=float, default=0.1,
                        help='Weight for entropy loss (VERL GRPO built-in)')
    parser.add_argument('--temperature', type=float,
                        default=1.0, help='Temperature for generation')
    parser.add_argument('--top_p', type=float,
                        default=0.9, help='Top-p sampling')
    parser.add_argument('--top_k', type=int, default=50, help='Top-k sampling')

    args = parser.parse_args()
    return args


def extract_options_from_question(question_text):
    import re
    match = re.search(r'a ([^?]+?) or a ([^?]+?)\?',
                      question_text, re.IGNORECASE)
    if match:
        return f"{match.group(1).strip()} or {match.group(2).strip()}"
    return None

def build_fortified_prompt_v3(original_question, options, enable_thinking=False):
    # # --- ROLE ---
    # role_section = "# ROLE\nYou are an expert AI that rewrites questions to probe the relationship between objects, making them easier for a Vision-Language Model to answer."

    # # --- INSTRUCTIONS ---
    # instructions = [
    #     "Your primary goal is to rewrite the user's question to be more descriptive and relationally aware.",
    #     "You CANNOT see the image. DO NOT invent specific details (e.g., color, size).",
    #     "Enrich options with general, common-sense visual descriptions.",
    #     # --- NEW RELATIONAL INSTRUCTION ---
    #     "**Crucial Step: Analyze Relationships.** Before writing, use your world knowledge to determine if one option typically contains, is part of, or is worn by another (e.g., `tray` and `muffin`, `person` and `necktie`).",
    #     "If a potential relationship exists, the rewritten question MUST guide the final model to consider this relationship."
    # ]

    # if options:
    #     options_json = json.dumps(options, ensure_ascii=False)
    #     instructions.append(
    #         f"For the provided options `{options_json}`:\n"
    #         "    - Preserve the exact option words and their original order.\n"
    #         "    - Enhance each option with a concise, universal description.\n"
    #         "    - **Relational Example**: For options ['tray', 'muffin'], a superior rewrite is: 'Does the image primarily show the **tray**, which is a container, or the **muffins**, which are the contents on the tray?' This phrasing forces the final model to acknowledge both elements."
    #     )

    # instructions_section = "# INSTRUCTIONS\n- " + "\n- ".join(instructions)
    
    # # --- OUTPUT FORMAT (remains the same) ---
    # if enable_thinking:
    #     output_format_section = (
    #         "# OUTPUT FORMAT\n"
    #         "1.  **Answer**: Inside `<answer>` tags, provide only the final, rewritten question."
    #     )
    # else:
    #     output_format_section = (
    #         "# OUTPUT FORMAT\n"
    #         "- Output **ONLY** the single, optimized question, wrapped in `<answer>` and `</answer>` tags."
    #     )
        
    # system_content = "\n\n".join([role_section, instructions_section, output_format_section])

    # # --- USER PROMPT ---
    # safe_question = repr(original_question)
    # user_content = f"Rewrite the following question, focusing on the relationship between the options.\n\nOriginal question: {safe_question}"
    role_section = "# ROLE\nYou are an expert AI that rewrites questions to help vision-language models make accurate visual distinctions."

    # --- INSTRUCTIONS ---
    instructions = [
        "Your task is to rewrite questions to be more precise and visually focused.",
        "You CANNOT see the image. Only add distinguishing features that are commonly known.",
        "Analyze the relationship between the two options and choose the best rewriting strategy:",
        "",
        "**Strategy 1: For related objects (one contains/is part of/is worn by another)**",
        "- Rewrite to ask about the relationship: 'Is this a person wearing a necktie, or just a necktie?'",
        "- Example: 'person' vs 'necktie' → 'Is this a person wearing a necktie, or just a necktie?'",
        "",
        "**Strategy 2: For visually similar objects**",
        "- Add the most distinguishing feature: 'Is this an apple (red fruit) or an orange (orange fruit)?'",
        "- Example: 'carrot' vs 'broccoli' → 'Is this a carrot (long orange vegetable) or broccoli (green flower-like vegetable)?'",
        "",
        "**Strategy 3: For completely different objects**",
        "- Keep it simple and direct: 'Is this a basket or a knife?'",
        "- Only add description if needed for clarity",
        "",
        "**Strategy 4: For objects that might be confused**",
        "- Add brief distinguishing feature: 'Is this a traffic light (with colored lights) or a bolt (metal fastener)?'",
        "",
        "**Important Rules:**",
        "- Keep the question concise and direct",
        "- Preserve the original option words",
        "- Only add distinguishing features when necessary",
        "- Focus on visual characteristics, not definitions"
    ]

    # 针对具体选项的处理
    if options and isinstance(options, list) and len(options) == 2:
        opt1, opt2 = options[0], options[1]
        
        # 分析选项关系并提供具体建议
        relationship_analysis = f"""
**Current Options:** '{opt1}' vs '{opt2}'

**Analysis Steps:**
1. Are these objects related? (one contains/is part of/is worn by another)
2. Are they visually similar? (same category, similar appearance)
3. Are they completely different? (different categories)
4. Could they be easily confused? (similar names or functions)

**Choose the appropriate strategy and rewrite accordingly.**
"""
        instructions.append(relationship_analysis)

    instructions_section = "# INSTRUCTIONS\n" + "\n".join(instructions)

    # --- OUTPUT FORMAT ---
    if enable_thinking:
        output_format_section = (
            "# OUTPUT FORMAT\n"
            "1. First, analyze the relationship between the options in <thinking> tags\n"
            "2. Then output ONLY the rewritten question in <answer> tags\n"
            "3. Do NOT include any other text or explanations"
        )
    else:
        output_format_section = (
            "# OUTPUT FORMAT\n"
            "- Output ONLY the single, optimized question, wrapped in <answer> and </answer> tags\n"
            "- Do NOT include any explanations, commentary, or additional text"
        )

    system_content = "\n\n".join([role_section, instructions_section, output_format_section])

    # --- USER PROMPT ---
    user_content = (
        "Rewrite the following question using the most appropriate strategy:\n\n"
        f"Original question: {original_question}"
    )

    return system_content, user_content

def build_optimized_prompt(original_question, options, enable_thinking=False):
    """
    Build optimized prompt for question optimization with improved structure.
    
    Args:
        original_question (str): The original question to optimize
        options (str): Available options for the question (can be None)
        enable_thinking (bool): Whether to enable thinking mode with explicit <thinking> output
    
    Returns:
        tuple: (system_content, user_content) - separated system and user messages
    """
    
    # System role content - defines who the model is and what it should do
    system_content = """You are an expert in optimizing questions for vision-language models.

Your task is to rewrite questions so they can be better understood and answered by vision-language models, focusing on clarity, specificity, and visual grounding.

**Core Responsibilities:**  
- Identify key visual elements and make them explicit in questions
- Use descriptive language prioritizing: color > shape > material/texture > size > spatial relations
- Avoid vague or ambiguous language that could confuse the model
- Preserve original option words and their order when comparing options
- Output ONLY the optimized question with no additional text or explanations

**Hard Rules:**
- **NEVER output any explanatory text, commentary, or additional information**
- **NEVER add background information or context beyond the question itself**
- **ALWAYS wrap the final answer in <answer> and </answer> tags**
- **NEVER assume object locations (e.g., 'center', 'main', 'middle') unless explicitly mentioned**
- **ALWAYS preserve original option words when comparing (e.g., 'watch', 'helmet')**

**Optimization Guidelines:**
• **Visual Specificity**: Make key objects/regions explicit in the question
• **Descriptive Language**: Use clear visual features (color, shape, texture, size)
• **Spatial Context**: Provide relevant spatial relations without location assumptions
• **Option Preservation**: Keep original option words and their order intact
• **Clarity Focus**: Avoid vague language that could confuse the model"""

    # Add options to system content if available
    if options:
        system_content += f"\n\n**Available Options**: {options}"
    
    # User content - the actual question to be processed
    user_content = f"Please optimize this question for better visual understanding:\n\n{original_question}"
    
    # If thinking mode is enabled, add explicit thinking instruction
    if enable_thinking:
        system_content += """

**Output Format (Thinking Mode):**
1. First output your thinking process wrapped in <thinking> and </thinking> tags
2. Then output the optimized question wrapped in <answer> and </answer> tags
3. Do not include any other text or explanations"""
        
        user_content += "\n\nPlease think through the optimization process step by step, then provide the optimized question."
    
    return system_content, user_content


def create_single_question_dataset(question, args, policy_tokenizer):
    """Create VERL dataset for a single question"""
    original_question = question['text'].replace('<location> ', '')
    options = extract_options_from_question(original_question)
    
    # Use the new optimized prompt builder
    system_content, user_content = build_fortified_prompt_v3(
        original_question, options, args.enable_thinking
    )
    
    # Create messages with separated system and user roles
    messages = [
        {"role": "system", "content": system_content},
        {"role": "user", "content": user_content}
    ]
    
    text = policy_tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True,
        enable_thinking=args.enable_thinking
    )

    # Create VERL dataset format
    dataset_data = [{
        "prompt": text,
        "original_question": original_question,
        "question_id": question['id'],
        "image_path": question['image_path'],
        "name": question['name'],
        "bbox": question['bbox'],
        "seg_mask": question['seg_mask'],
        "center_point": question['center_point'],
        "scribble": question['scribble'],
        "label": question['name'],
    }]

    logger.debug(
        f"[DEBUG] Created dataset for question {question['id']} with {len(dataset_data)} samples")
    logger.debug(f"[DEBUG] Dataset size: {len(dataset_data)}")
    return dataset_data


def compute_roc_loss_with_policy(completion, qid, image_path, text, name, bbox, seg_mask, center_point, scribble, label,
                                 llava_model, llava_processor, device, args):
    """Compute loss using the policy-optimized prompt"""

    logger.debug(f"[DEBUG] Computing loss for question {qid}")
    logger.debug(f"[DEBUG] Completion: {completion}")

    import re

    where_from = "answer"
    matches = list(re.finditer(r"<answer>(.*?)</answer>", completion, re.DOTALL))
    if matches:
        optimized_question = matches[-1].group(1).strip()
    else:
        # If no <answer> tags found, try to extract from thinking mode output
        thinking_matches = list(re.finditer(r"<thinking>(.*?)</thinking>", completion, re.DOTALL))
        if thinking_matches:
            # Extract the last thinking block and look for any remaining content
            thinking_end = thinking_matches[-1].end()
            remaining_content = completion[thinking_end:].strip()
            if remaining_content:
                optimized_question = remaining_content
                where_from = "thinking"
            else:
                optimized_question = completion.strip()
                where_from = "original"
        elif "Optimized:" in completion:
            optimized_question = completion.split("Optimized:")[-1].strip()
            where_from = "optimized"
        else:
            optimized_question = completion.strip()
            where_from = "original"

    logger.debug(
        f"[DEBUG] Optimized question: {optimized_question} from {where_from}")

    # 如果提取的问题为空，使用原始问题
    if not optimized_question or optimized_question.strip() == "":
        optimized_question = text
        logger.debug(
            f"[DEBUG] Empty optimized question, using original: {optimized_question}")

    # Load and process image
    image_path_full = os.path.join(
        args.data_path, 'image', image_path.split('/')[-2], image_path.split('/')[-1])
    image = Image.open(image_path_full)
    iw, ih = image.size

    # Create prompt with optimized question
    prompt = f"USER: <image>\n{optimized_question} ASSISTANT:"

    # 清除之前的缓存
    get_local.clear()

    inputs = llava_processor(text=prompt, images=image,
                             return_tensors="pt").to(device)
    # 获取图像token索引
    img_token_positions = torch.where(inputs['input_ids'] == 32000)

    if len(img_token_positions[0]) > 0:
        # 确保有匹配的元素
        if img_token_positions[1].numel() > 0:
            img_token_idx = img_token_positions[1][0].item()
            logger.debug(
                f"[DEBUG] Found image token at index: {img_token_idx}")
        else:
            img_token_idx = 1
            logger.warning(
                f"[WARNING] No image token found in positions, using default index {img_token_idx}")
    else:
        # 如果没有找到图像token，使用默认值
        logger.error(f"[ERROR] No image token found for question {qid}")
        sys.exit(1)

    # Create mask based on visual prompt type
    mask = np.zeros((ih, iw))

    if args.visual_prompt == 'Box':
        x_min, y_min, x_max, y_max = int(bbox[0]), int(
            bbox[1]), int(bbox[0] + bbox[2]), int(bbox[1] + bbox[3])
        mask[y_min: y_max, x_min: x_max] = 1
        mask = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(
                args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.CenterCrop(args.n_px),
            transforms.Resize(
                args.H, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor(),
        ])(mask)[0]
        logger.debug(f"[DEBUG] Box mask created: bbox={bbox}")

    elif args.visual_prompt == 'Mask':
        mask_path = os.path.join(args.data_path, 'mask', seg_mask)
        mask = Image.open(mask_path)
        logger.debug(
            f"[DEBUG][Mask] mask sum after load: {np.array(mask).sum()}, dtype: {np.array(mask).dtype}, shape: {np.array(mask).shape}")
        mask = transforms.Compose([
            transforms.Resize(
                args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.CenterCrop(args.n_px),
            transforms.Resize(
                args.H, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor(),
        ])(mask)[0]
        logger.debug(
            f"[DEBUG][Mask] mask sum after transform: {mask.sum()}, dtype: {mask.dtype}, shape: {mask.shape}")
        logger.debug(f"[DEBUG] Mask loaded: {mask_path}")

    elif args.visual_prompt == 'Scribble':
        for scri in scribble:
            mask[int(scri[1]), int(scri[0])] = 1
        mask = ((1-mask) * 255).astype(np.uint8)
        distance_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        distance_transform_normalized = cv2.normalize(
            distance_transform, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        mask = distance_transform_normalized
        mask = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(
                args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.CenterCrop(args.n_px),
            transforms.Resize(
                args.H, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor(),
        ])(mask)[0]
        logger.debug(f"[DEBUG] Scribble mask created: {len(scribble)} points")

    elif args.visual_prompt == 'Point':
        mask[int(center_point[1]), int(center_point[0])] = 1
        mask = ((1-mask) * 255).astype(np.uint8)
        distance_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        distance_transform_normalized = cv2.normalize(
            distance_transform, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        mask = distance_transform_normalized
        mask = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(
                args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.CenterCrop(args.n_px),
            transforms.Resize(
                args.H, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor(),
        ])(mask)[0]
        logger.debug(f"[DEBUG] Point mask created: center={center_point}")

    mask = mask.cuda() if torch.cuda.is_available() else mask

    # Generate with LLaVA model
    with torch.no_grad():
        get_local.clear()
        outputs = llava_model.generate(
            **inputs,
            max_new_tokens=30,
            return_dict_in_generate=True,
            output_scores=True,
            do_sample=True,
            output_attentions=True,
            pad_token_id=llava_processor.tokenizer.eos_token_id
        )

        # 从GenerateDecoderOnlyOutput中提取sequences
        if hasattr(outputs, 'sequences'):
            generated_sequences = outputs.sequences
            decoded_output = llava_processor.batch_decode(
                generated_sequences, skip_special_tokens=True)[0]
            logger.debug(f"[DEBUG] Decoded output: {decoded_output}")
        else:
            logger.debug(
                "[DEBUG] outputs has no sequences, attributes:", dir(outputs))

        # 尝试获取注意力图，使用实际的cache key
        attention_keys = [
            'force_eager_attention_with_decorator.<locals>.decorated_eager_attention_forward', 'eager_attention_forward']
        ori_attention_maps = None

        for key in attention_keys:
            if key in get_local.cache:
                ori_attention_maps = get_local.cache[key]
                if isinstance(ori_attention_maps, list):
                    valid_maps = 0
                    for i, att in enumerate(ori_attention_maps):
                        if att is not None:
                            valid_maps += 1
                        else:
                            logger.debug(f"[DEBUG] Attention map {i} is None")
                    logger.debug(
                        f"[DEBUG] Valid attention maps: {valid_maps}/{len(ori_attention_maps)}")
                break

        if ori_attention_maps is None:
            logger.error(f"[ERROR] No attention maps found in cache")
            logger.debug(
                f"[DEBUG] Available cache keys: {list(get_local.cache.keys())}")
            logger.debug(
                f"[DEBUG] Cache contents: {[(k, len(v) if isinstance(v, list) else type(v)) for k, v in get_local.cache.items()]}")
            sys.exit(1)

        # 处理attention maps
        attention_maps = [att for i, att in enumerate(
            ori_attention_maps) if att is not None and att.shape[-2] > 1]

        if len(attention_maps) > 0:
            mean_att = torch.cat([att.to(device)
                                 for att in attention_maps], 0).mean(0)
        else:
            logger.error(f"[ERROR] No valid attention maps found")
            sys.exit(1)

        if args.show_att:
            fig = show_image_relevance(
                mean_att[:, img_token_idx + args.H * args.W:, img_token_idx:img_token_idx + args.H * args.W].mean(axis=0).mean(axis=0),
                image,
                orig_image=image,
                mask=mask,
                preprocess=preprocess,
                only_map=True,
                show_mask=True
            )
            fig.savefig(f'vis/img_tmp_{qid}.png', dpi=300, bbox_inches='tight')

        target2img_rel = mean_att[:, img_token_idx + args.H * args.W:,
                                  img_token_idx:img_token_idx + args.H * args.W].mean(axis=0).mean(axis=0).unsqueeze(0)
        
        loss = args.alpha * compute_ca_loss(target2img_rel.to(mask.device), masks=[
                                            mask], choice=args.visual_prompt, object_positions=None)

        logger.debug(f"[DEBUG] Loss computed: {loss.item()}")

        get_local.clear()
        torch.cuda.empty_cache()

        if (target2img_rel.reshape(-1).sum(dim=-1) == 0).any():
            logger.warning("Warning: attention map sum is zero!")
        if (mask.sum() == 0):
            logger.warning("Warning: mask sum is zero!")

        if torch.isnan(loss).any() or torch.isinf(loss).any():
            logger.error("Loss is nan/inf!", loss)
            return 0.0

        return -loss.item()


def create_roc_reward_function(llava_model, llava_processor, device, args):
    """Create a custom reward function for ROC dataset that works with VERL"""
    
    def roc_reward_function(data, **kwargs):
        """Custom reward function for ROC dataset"""
        # Extract completions from data
        completions = data.get('completions', [])
        question_info = data.get('question_info', {})
        
        rewards = []
        for completion in completions:
            reward = compute_roc_loss_with_policy(
                completion,
                question_info.get('id', 'unknown'),
                question_info.get('image_path', ''),
                question_info.get('text', '').replace('<location> ', ''),
                question_info.get('name', ''),
                question_info.get('bbox', []),
                question_info.get('seg_mask', ''),
                question_info.get('center_point', []),
                question_info.get('scribble', []),
                question_info.get('name', ''),
                llava_model,
                llava_processor,
                device,
                args
            )
            rewards.append(reward)
        
        return rewards
    
    return roc_reward_function


def create_verl_config(args):
    """Create VERL configuration for GRPO training"""
    from omegaconf import OmegaConf
    
    # Base configuration for GRPO
    config = OmegaConf.create({
        "algorithm": {
            "adv_estimator": "grpo",
            "norm_adv_by_std_in_grpo": True,
            "use_kl_in_reward": False,
        },
        "data": {
            "train_batch_size": 1,  # Single question at a time
            "max_prompt_length": 512,
            "max_response_length": 128,
        },
        "actor_rollout_ref": {
            "rollout": {
                "n": args.num_generations,  # Group size for GRPO
            },
            "actor": {
                "ppo_mini_batch_size": args.num_generations,
                "ppo_epochs": 1,
                "clip_ratio": args.epsilon,
                "use_kl_loss": True,
                "kl_loss_coef": 0.001,
                "kl_loss_type": "low_var_kl",
                "entropy_coeff": args.entropy_weight,
                "optim": {
                    "lr": args.lr,
                },
            },
        },
        "trainer": {
            "max_steps": args.T,
            "logging_steps": 1,
            "save_freq": 1,
        }
    })
    
    return config


def train_policy_for_single_question(question, policy_model, policy_tokenizer, llava_model, llava_processor, device, args):
    """Train policy model for a single question using VERL's official GRPO approach"""

    logger.info(
        f"[INFO] Starting VERL GRPO training for question {question['id']}")

    # Create VERL configuration
    config = create_verl_config(args)
    
    # Create custom reward function
    reward_function = create_roc_reward_function(llava_model, llava_processor, device, args)
    
    # Create dataset for this question
    dataset_data = create_single_question_dataset(question, args, policy_tokenizer)
    
    # For test-time optimization, we'll use a simplified approach
    # that mimics VERL's GRPO but without the full distributed training setup
    best_completion = None
    best_reward = float('-inf')
    
    for step in range(args.T):
        logger.debug(f"[DEBUG] GRPO step {step + 1}/{args.T}")
        
        # Generate group of completions (GRPO group sampling)
        completions = []
        for _ in range(args.num_generations):
            prompt = dataset_data[0]['prompt']
            inputs = policy_tokenizer([prompt], return_tensors="pt").to(device)
            
            with torch.no_grad():
                outputs = policy_model.generate(
                    **inputs,
                    max_new_tokens=args.max_completion_length,
                    do_sample=True,
                    temperature=args.temperature,
                    top_p=args.top_p,
                    top_k=args.top_k,
                    pad_token_id=policy_tokenizer.eos_token_id,
                    eos_token_id=policy_tokenizer.eos_token_id,
                )
            
            completion = policy_tokenizer.decode(outputs[0])
            completions.append(completion)
        
        # Evaluate completions using reward function (GRPO reward assignment)
        data = {
            'completions': completions,
            'question_info': dataset_data[0]
        }
        rewards = reward_function(data)
        
        # Find best completion (GRPO baseline calculation)
        for i, reward in enumerate(rewards):
            if reward > best_reward:
                best_reward = reward
                best_completion = completions[i]
        
        logger.debug(f"[DEBUG] Step {step + 1} best reward: {best_reward:.4f}")
        
        # In a full VERL implementation, this would update the policy
        # For test-time optimization, we just track the best completion
    
    logger.info(f"[INFO] Best completion found with reward: {best_reward:.4f}")
    
    return best_completion, best_reward


def generate_optimized_prompt(question, trained_policy_model, policy_tokenizer, device, args):
    """Generate optimized prompt using the trained policy model"""
    original_question = question['text'].replace('<location> ', '')
    options = extract_options_from_question(original_question)
    
    # Use the new optimized prompt builder
    system_content, user_content = build_fortified_prompt_v3(
        original_question, options, args.enable_thinking
    )
    
    # Create messages with separated system and user roles
    messages = [
        {"role": "system", "content": system_content},
        {"role": "user", "content": user_content}
    ]
    
    text = policy_tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True,
        enable_thinking=args.enable_thinking
    )
    inputs = policy_tokenizer([text], return_tensors="pt").to(device)

    with torch.no_grad():
        outputs = trained_policy_model.generate(
            **inputs,
            max_new_tokens=args.max_completion_length,
            do_sample=True,
            temperature=args.temperature,
            top_p=args.top_p,
            top_k=args.top_k,
            pad_token_id=policy_tokenizer.eos_token_id,
            eos_token_id=policy_tokenizer.eos_token_id,
            bos_token_id=getattr(policy_tokenizer, 'bos_token_id', None)
        )

    optimized_prompt = policy_tokenizer.decode(outputs[0])
    logger.debug(f"[DEBUG] Raw policy output: {optimized_prompt}")

    import re
    
    # First try to extract from <answer> tags
    matches = list(re.finditer(r"<answer>(.*?)</answer>", optimized_prompt, re.DOTALL))
    if matches:
        optimized_question = matches[-1].group(1).strip()
        logger.debug(f"[DEBUG] Extracted optimized question from <answer> tags: {optimized_question}")
    else:
        # If no <answer> tags found, try to extract from thinking mode output
        if args.enable_thinking:
            # Look for content after <thinking> tags
            thinking_matches = list(re.finditer(r"<thinking>(.*?)</thinking>", optimized_prompt, re.DOTALL))
            if thinking_matches:
                # Extract the last thinking block and look for any remaining content
                thinking_end = thinking_matches[-1].end()
                remaining_content = optimized_prompt[thinking_end:].strip()
                if remaining_content:
                    optimized_question = remaining_content
                    logger.debug(f"[DEBUG] Extracted optimized question after thinking: {optimized_question}")
                else:
                    optimized_question = original_question
                    logger.debug(f"[DEBUG] No content after thinking tags, using original")
            else:
                optimized_question = original_question
                logger.debug(f"[DEBUG] No <thinking> tags found, using original")
        else:
            optimized_question = original_question
            logger.debug(f"[DEBUG] No <answer> tags found, using original")

    return optimized_question


def generate_original_llava_output(question, llava_model, llava_processor, device, args):
    """Generate original LLaVA output without any optimization"""

    original_question = question['text'].replace('<location> ', '')
    image_path = os.path.join(args.data_path, 'image', question['image_path'].split(
        '/')[-2], question['image_path'].split('/')[-1])
    image = Image.open(image_path)

    prompt = f"USER: <image>\n{original_question} ASSISTANT:"
    inputs = llava_processor(text=prompt, images=image,
                             return_tensors="pt").to(device)

    with torch.no_grad():
        outputs = llava_model.generate(
            **inputs,
            max_new_tokens=30,
            do_sample=False,
            pad_token_id=llava_processor.tokenizer.eos_token_id
        )

    output = llava_processor.batch_decode(outputs, skip_special_tokens=True)[0]
    return output


def force_eager_attention_with_decorator(model):
    """强制将所有LlamaDecoderLayer的self_attn替换为eager实现，并添加get_local装饰器"""
    replaced = 0
    from transformers.models.llama.modeling_llama import LlamaDecoderLayer, LlamaAttention, eager_attention_forward

    original_eager_attention_forward = eager_attention_forward

    @get_local('attn_weights')
    def decorated_eager_attention_forward(module, query, key, value, attention_mask, scaling, dropout=0.0, **kwargs):
        key_states = repeat_kv(key, module.num_key_value_groups)
        value_states = repeat_kv(value, module.num_key_value_groups)

        attn_weights = torch.matmul(
            query, key_states.transpose(2, 3)) * scaling
        if attention_mask is not None:
            causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]
            attn_weights = attn_weights + causal_mask

        attn_weights = nn.functional.softmax(
            attn_weights, dim=-1, dtype=torch.float32).to(query.dtype)
        attn_weights = nn.functional.dropout(
            attn_weights, p=dropout, training=module.training)
        attn_output = torch.matmul(attn_weights, value_states)
        attn_output = attn_output.transpose(1, 2).contiguous()

        return attn_output, attn_weights

    # 设置正确的__qualname__
    decorated_eager_attention_forward.__qualname__ = 'eager_attention_forward'

    # 替换eager_attention_forward函数
    import transformers.models.llama.modeling_llama as llama_module
    llama_module.eager_attention_forward = decorated_eager_attention_forward

    # 强制使用eager实现
    for name, module in model.named_modules():
        if isinstance(module, LlamaDecoderLayer):
            # 强制设置attention实现为eager
            module.self_attn.config._attn_implementation = "eager"
            replaced += 1

    logger.debug(
        f"[DEBUG] Forced {replaced} LlamaDecoderLayer to use eager attention implementation and decorated eager_attention_forward")
    return model


def print_cuda_memory(tag=""):
    if torch.cuda.is_available():
        logger.info(f"[CUDA][{tag}] Allocated: {torch.cuda.memory_allocated() / 1024**2:.2f} MB, "
                    f"Reserved: {torch.cuda.memory_reserved() / 1024**2:.2f} MB, "
                    f"Max Allocated: {torch.cuda.max_memory_allocated() / 1024**2:.2f} MB, "
                    f"Max Reserved: {torch.cuda.max_memory_reserved() / 1024**2:.2f} MB")


def generate_experiment_filename(args, base_filename):
    """
    Generate filename with experiment configuration information
    
    Args:
        args: parsed arguments containing experiment parameters
        base_filename: base filename without extension
    
    Returns:
        str: filename with experiment configuration
    """
    import datetime
    
    # Extract key configuration parameters
    config_parts = []
    
    # Method identifier
    config_parts.append("verl")
    
    # Visual prompt type
    config_parts.append(f"vp{args.visual_prompt}")
    
    # Key hyperparameters
    config_parts.append(f"T{args.T}")
    config_parts.append(f"lr{args.lr}")
    config_parts.append(f"eps{args.epsilon}")
    config_parts.append(f"alpha{args.alpha}")
    config_parts.append(f"n_gen{args.num_generations}")
    config_parts.append(f"ent{args.entropy_weight}")
    
    # Generation parameters
    config_parts.append(f"temp{args.temperature}")
    config_parts.append(f"top_p{args.top_p}")
    config_parts.append(f"top_k{args.top_k}")
    
    # Thinking mode
    if args.enable_thinking:
        config_parts.append("thinking")
    
    # Timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    config_parts.append(timestamp)
    
    # Combine all parts
    config_str = "_".join(config_parts)
    
    return f"{base_filename}_{config_str}"


def main():
    

    logger.info(f"[INFO] Starting VERL Test-time GRPO ControlMLLM")
    logger.info(f"[INFO] Visual Prompt: {args.visual_prompt}")
    logger.info(f"[INFO] T iterations per question: {args.T}")
    logger.info(f"[INFO] Entropy weight: {args.entropy_weight}")
    
    # Create vis directory if show_att is enabled
    if args.show_att:
        os.makedirs('vis', exist_ok=True)

    # Load LLaVA model and processor
    logger.info("[INFO] Loading LLaVA model...")
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
    )

    llava_model = LlavaForConditionalGeneration.from_pretrained(
        args.model_path,
        quantization_config=quantization_config,
        device_map="auto"
    )

    llava_processor = AutoProcessor.from_pretrained(args.model_path)
    llava_processor.patch_size = 14
    device = "cuda" if torch.cuda.is_available() else "cpu"

    # 确保get_local被激活
    get_local.activate()
    logger.debug(f"[DEBUG] get_local activated: {get_local.is_activate}")

    # 检查模型配置
    logger.debug(
        f"[DEBUG] Model config attention implementation: {llava_model.config._attn_implementation}")
    logger.debug(
        f"[DEBUG] Model config model type: {llava_model.config.model_type}")
    logger.debug(
        f"[DEBUG] Model config architecture: {llava_model.config.architectures}")

    # 强制eager实现并添加get_local装饰器
    llava_model.config._attn_implementation = "eager"
    llava_model = force_eager_attention_with_decorator(llava_model)

    logger.info(f"[INFO] LLaVA model loaded on {device}")

    # Freeze LLaVA model parameters
    for p in llava_model.parameters():
        p.requires_grad = False

    # Load questions
    logger.info("[INFO] Loading questions...")
    questions = [json.loads(q) for q in open(args.question_file, "r")]
    logger.info(f"[INFO] Loaded {len(questions)} questions")

    # Process each question individually with test-time GRPO
    base, ext = os.path.splitext(os.path.expanduser(args.answers_file))
    # Generate filename with experiment configuration
    config_filename = generate_experiment_filename(args, base)
    answers_file = f"{config_filename}{ext}"
    os.makedirs(os.path.dirname(answers_file), exist_ok=True)

    if args.resume and os.path.exists(answers_file):
        answers = [json.loads(q) for q in open(answers_file, "r")]
        answer_ids = [q['question_id'] for q in answers]
    else:
        answer_ids = []

    if args.resume:
        ans_file = open(answers_file, "a")
    else:
        ans_file = open(answers_file, "w")

    # Load policy model (Qwen3-4B) 只加载一次
    logger.info("[INFO] Loading policy model (once)...")
    policy_tokenizer = AutoTokenizer.from_pretrained(args.policy_model_path)
    policy_model = AutoModelForCausalLM.from_pretrained(
        args.policy_model_path,
        torch_dtype=torch.float16,
        device_map="auto"
    )
    # 冻结所有参数
    for param in policy_model.parameters():
        param.requires_grad = False
    # 插入LoRA adapter
    lora_config = LoraConfig(
        r=8,
        lora_alpha=16,
        target_modules=["q_proj", "v_proj"],
        lora_dropout=0.05,
        bias="none",
        task_type=TaskType.CAUSAL_LM
    )
    policy_model = get_peft_model(policy_model, lora_config)
    policy_model.print_trainable_parameters()
    emb = policy_model.get_input_embeddings().weight.data
    logger.debug(
        f"Embedding stats: min={emb.min().item()}, max={emb.max().item()}, mean={emb.mean().item()}, nan={torch.isnan(emb).any().item()}, inf={torch.isinf(emb).any().item()}")
    policy_model_init_state = copy.deepcopy(policy_model.state_dict())

    logger.info("Processing questions with VERL test-time GRPO...")
    answers_buffer = []
    for idx, q in enumerate(tqdm(questions, desc="Processing Questions")):
        qid = q['id']
        if args.resume and answer_ids and qid in answer_ids:
            continue
        logger.info(f"\n[INFO] Processing question {qid}")

        # Step 0: Generate original LLaVA output (without optimization)
        logger.info(
            f"[INFO] Step 0: Generating original LLaVA output for question {qid}")
        original_output = generate_original_llava_output(
            q, llava_model, llava_processor, device, args)
        logger.debug(
            f"[DEBUG] Original LLaVA output: {original_output[:100]}...")

        # Step 1: 每个问题优化前重置policy model参数
        logger.info(
            f"[INFO] Step 1: Resetting policy model parameters for question {qid}")
        policy_model.load_state_dict(policy_model_init_state)

        # Step 2: Train policy model for this specific question using VERL
        logger.info(
            f"[INFO] Step 2: Training policy model for question {qid} using VERL")
        trained_policy_model, best_reward = train_policy_for_single_question(
            q, policy_model, policy_tokenizer, llava_model, llava_processor, device, args
        )

        # Step 3: Generate optimized prompt using the trained policy model
        logger.info(
            f"[INFO] Step 3: Generating optimized prompt for question {qid}")
        optimized_question = generate_optimized_prompt(
            q, policy_model, policy_tokenizer, device, args)

        logger.debug(
            f"[DEBUG] Original question: {q['text'].replace('<location> ', '')}")
        logger.debug(f"[DEBUG] Optimized question: {optimized_question}")

        # Step 4: Use optimized prompt with LLaVA model for final answer
        logger.info(
            f"[INFO] Step 4: Generating optimized LLaVA output for question {qid}")
        image_path = os.path.join(args.data_path, 'image', q['image_path'].split(
            '/')[-2], q['image_path'].split('/')[-1])
        image = Image.open(image_path)
        logger.info(image)
        prompt = f"USER: <image>\n{optimized_question} ASSISTANT:"
        inputs = llava_processor(
            text=prompt, images=image, return_tensors="pt").to(device)

        with torch.no_grad():
            outputs = llava_model.generate(
                **inputs,
                max_new_tokens=30,
                do_sample=False,
                pad_token_id=llava_processor.tokenizer.eos_token_id
            )

        optimized_output = llava_processor.batch_decode(
            outputs, skip_special_tokens=True)[0]
        logger.debug(f"[DEBUG] Optimized LLaVA output: {optimized_output}")

        # Create output_T list similar to original llava_roc.py
        # First: original, Second: optimized
        output_T = [original_output, optimized_output]

        # Save results with output_T format
        answers_buffer.append({
            "question_id": qid,
            # List with [original_output, optimized_output]
            "answers": output_T,
            "relevancy": [[0, 0], [0, 0]],  # Placeholder relevancy scores
            "label": q['name']
        })
        # 每1个问题写入一次
        if len(answers_buffer) >= 1:
            for ans in answers_buffer:
                ans_file.write(json.dumps(ans) + '\n')
            ans_file.flush()
            answers_buffer = []
    # 处理剩余未写入的
    if answers_buffer:
        for ans in answers_buffer:
            ans_file.write(json.dumps(ans) + '\n')
        ans_file.flush()

    ans_file.close()
    logger.info("VERL processing completed!")


if __name__ == "__main__":
    import datetime
    args = parse_args()
    base, ext = os.path.splitext(os.path.expanduser(args.answers_file))
    thinking_tag = "enable_thinking" if args.enable_thinking else "no_thinking"
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = os.path.join("logs", args.visual_prompt)
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(
        log_dir,
        f"llava_roc_verl_debug_{args.visual_prompt}_{thinking_tag}_{timestamp}.log"
    )
    logging.basicConfig(
        filename=log_path,
        filemode='a',
        format='%(asctime)s %(levelname)s: %(message)s',
        level=logging.DEBUG
    )
    logger = logging.getLogger(__name__)
    main()
