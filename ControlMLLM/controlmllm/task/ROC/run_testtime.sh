# export CUDA_VISIBLE_DEVICES=4,5
# export TOKENIZERS_PARALLELISM=false
# python llava_roc_testtime.py \
#     --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
#     --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
#     --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
#     --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
#     --visual_prompt "Box" \
#     --answers_file "outputs/llava_roc_testtime_thinking.json"\
#     --T 4 \
#     --num_generations 6 \
#     --max_completion_length 1024 \
#     --lr 1e-6 \
#     --epsilon 0.1 \
#     --alpha 400\
#     --enable_thinking

# export CUDA_VISIBLE_DEVICES=4,5
# export TOKENIZERS_PARALLELISM=false
# python llava_roc_testtime.py \
#     --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
#     --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
#     --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
#     --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
#     --visual_prompt "Box" \
#     --answers_file "outputs/llava_roc_testtime_nothinking.json"\
#     --T 6 \
#     --num_generations 8 \
#     --max_completion_length 128 \
#     --lr 1e-6 \
#     --epsilon 0.1 \
#     --alpha 400\
#     --resume \




# export CUDA_VISIBLE_DEVICES=6,7
# export TOKENIZERS_PARALLELISM=false
# python llava_roc_testtime.py \
#     --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
#     --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
#     --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
#     --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
#     --visual_prompt "Box" \
#     --answers_file "outputs/llava_roc_testtime_nothinking_4B.json"\
#     --T 4 \
#     --num_generations 8 \
#     --max_completion_length 128 \
#     --lr 1e-6 \
#     --epsilon 0.2 \
#     --alpha 400 \


# export CUDA_VISIBLE_DEVICES=4,5
# export TOKENIZERS_PARALLELISM=false
# python llava_roc_testtime.py \
#     --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
#     --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
#     --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
#     --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
#     --visual_prompt "Box" \
#     --answers_file "outputs/llava_roc_testtime_nothinking_4B_6T.json"\
#     --T 6 \
#     --num_generations 8 \
#     --max_completion_length 128 \
#     --lr 1e-6 \
#     --epsilon 0.2 \
#     --alpha 400 \

export CUDA_VISIBLE_DEVICES=4,5
export TOKENIZERS_PARALLELISM=false
python llava_roc_testtime.py \
    --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
    --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
    --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
    --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
    --visual_prompt "Box" \
    --answers_file "outputs/llava_roc_testtime_nothinking_4B_T.json" \
    --T 4 \
    --num_generations 4 \
    --max_completion_length 128 \
    --lr 1e-6 \
    --epsilon 0.1 \
    --alpha 400 \
    --resume

# export CUDA_VISIBLE_DEVICES=6,7
# export TOKENIZERS_PARALLELISM=false
# python llava_roc_testtime.py \
#     --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
#     --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
#     --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
#     --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
#     --visual_prompt "Box" \
#     --answers_file "outputs/llava_roc_testtime_nothinking_4B_T.json" \
#     --T 1 \
#     --num_generations 8 \
#     --max_completion_length 128 \
#     --lr 1e-6 \
#     --epsilon 0.1 \
#     --alpha 400 