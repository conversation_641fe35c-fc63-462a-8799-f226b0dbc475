2025-07-10 15:14:33,049 INFO: [INFO] Starting Test-time GRPO ControlMLLM
2025-07-10 15:14:33,049 INFO: [INFO] Visual Prompt: Mask
2025-07-10 15:14:33,049 INFO: [INFO] T iterations per question: 6
2025-07-10 15:14:33,049 INFO: [INFO] Loading LLaVA model...
2025-07-10 15:14:33,304 DEBUG: Loading bitsandbytes native library from: /data1/mrwu_tmp/miniconda3/envs/controlmllm/lib/python3.9/site-packages/bitsandbytes/libbitsandbytes_cuda126.so
2025-07-10 15:14:37,232 DEBUG: [DEBUG] get_local activated: True
2025-07-10 15:14:37,232 DEBUG: [DEBUG] Model config attention implementation: sdpa
2025-07-10 15:14:37,232 DEBUG: [DEBUG] Model config model type: llava
2025-07-10 15:14:37,232 DEBUG: [DEBUG] Model config architecture: ['LlavaForConditionalGeneration']
2025-07-10 15:14:37,237 DEBUG: [DEBUG] Forced 32 LlamaDecoderLayer to use eager attention implementation and decorated eager_attention_forward
2025-07-10 15:14:37,237 INFO: [INFO] LLaVA model loaded on cuda
2025-07-10 15:14:37,239 INFO: [INFO] Loading questions...
2025-07-10 15:14:37,428 INFO: [INFO] Loaded 1748 questions
2025-07-10 15:14:37,428 INFO: [INFO] Loading policy model (once)...
2025-07-10 15:14:39,604 DEBUG: Embedding stats: min=-0.2216796875, max=0.25, mean=2.9325485229492188e-05, nan=False, inf=False
2025-07-10 15:14:39,673 INFO: Processing questions with test-time GRPO...
2025-07-10 15:14:39,673 INFO: 
[INFO] Processing question 163616
2025-07-10 15:14:39,673 INFO: [INFO] Step 0: Generating original LLaVA output for question 163616
2025-07-10 15:14:41,042 DEBUG: [DEBUG] Original LLaVA output: USER:  
Is the object of the image a skirt or a jersey? ASSISTANT: The object of the image is a skir...
2025-07-10 15:14:41,042 INFO: [INFO] Step 1: Resetting policy model parameters for question 163616
2025-07-10 15:14:41,058 INFO: [INFO] Step 2: Training policy model for question 163616
2025-07-10 15:14:41,058 INFO: [INFO] Starting test-time GRPO for question 163616
2025-07-10 15:14:41,075 DEBUG: [DEBUG] Created dataset for question 163616 with 1 samples
2025-07-10 15:14:41,085 DEBUG: [DEBUG] Dataset size: 1
2025-07-10 15:14:41,085 DEBUG: [DEBUG] Dataset columns: ['prompt', 'original_question', 'question_id', 'image_path', 'name', 'bbox', 'seg_mask', 'center_point', 'scribble', 'label']
2025-07-10 15:14:41,219 INFO: [INFO] Training policy model for question 163616...
