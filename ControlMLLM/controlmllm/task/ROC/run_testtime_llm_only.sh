export CUDA_VISIBLE_DEVICES=6,7
export TOKENIZERS_PARALLELISM=false
python llava_roc_testtime_llm_only.py \
    --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
    --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
    --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
    --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
    --visual_prompt "Box" \
    --answers_file "outputs/llava_roc_testtime_llm_only_nothinking_4B.json" \
    --resume


# export CUDA_VISIBLE_DEVICES=4,5,6,7
# export TOKENIZERS_PARALLELISM=false
# python llava_roc_testtime_llm_only.py \
#     --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
#     --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-32B" \
#     --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
#     --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
#     --visual_prompt "Box" \
#     --answers_file "outputs/llava_roc_testtime_llm_only_thinking.json" \
#     --resume \
#     --enable_thinking