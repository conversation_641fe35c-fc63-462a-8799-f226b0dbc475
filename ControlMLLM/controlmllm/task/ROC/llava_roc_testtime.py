import os, sys
sys.setrecursionlimit(20000)
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from visualizer import get_local
get_local.activate()
import argparse
from PIL import Image
import torch
import gc;
from transformers import AutoProcessor, LlavaForConditionalGeneration, BitsAndBytesConfig, AutoTokenizer, AutoModelForCausalLM
from utils import compute_ca_loss, show_image_relevance
import torchvision.transforms as transforms
import numpy as np
import json
import cv2
from tqdm import tqdm
import torch.nn.functional as F
from transformers.models.llama.modeling_llama import LlamaAttention
import torch.nn as nn
import torch.nn.functional as F
import math
from transformers.models.llama.modeling_llama import repeat_kv, apply_rotary_pos_emb, eager_attention_forward, ALL_ATTENTION_FUNCTIONS
import copy
from peft import LoraConfig, get_peft_model, TaskType
import logging
from functools import partial
import clip
_, preprocess = clip.load("ViT-B/32", device='cpu', jit=False)
try:
    from trl import GRPOTrainer
    from trl.trainer.grpo_config import GRPOConfig
    from datasets import Dataset
except ImportError as e:
    print(f"Warning: Could not import TRL components: {e}")
    sys.exit(1)

def parse_args():
    parser = argparse.ArgumentParser(description="Test-time GRPO ControlMLLM")
    
    # Model paths
    parser.add_argument('--model_path', type=str, default="pretrained_models/llava-1.5-7b-hf", help='Path to the pretrained model')
    parser.add_argument('--policy_model_path', type=str, default="Qwen/Qwen2.5-4B-Instruct", help='Path to the policy model')
    parser.add_argument('--data_path', type=str, default="data/ROC/LVIS", help='Path to the dataset')
    parser.add_argument('--question_file', type=str, default='data/ROC/question_roc.json', help='Path to the question file')
    parser.add_argument('--answers_file', type=str, default='outputs/llava_roc_testtime.json', help='Path to the answers file')
    
    # Parameters
    parser.add_argument('--visual_prompt', type=str, default='Box', help='Visual prompt method')
    parser.add_argument('--H', type=int, default=24, help='Height of the attention map')
    parser.add_argument('--W', type=int, default=24, help='Width of the attention map')
    parser.add_argument('--n_px', type=int, default=224, help='Input size of image')
    parser.add_argument('--alpha', type=float, default=400, help='Alpha parameter')
    parser.add_argument('--T', type=int, default=5, help='T parameter for test-time GRPO')
    parser.add_argument('--lr', type=float, default=1e-6, help='Learning rate for policy model')
    parser.add_argument('--epsilon', type=float, default=0.2, help='GRPO epsilon parameter')
    parser.add_argument('--num_generations', type=int, default=4, help='Number of generations per prompt')
    parser.add_argument('--max_completion_length', type=int, default=30, help='Maximum completion length')
    parser.add_argument('--resume', action='store_true', help='Resume from checkpoint')
    parser.add_argument('--enable_thinking', action='store_true', help='Enable Qwen3 thinking mode in prompt')
    parser.add_argument('--show_att', action='store_true', help='Flag to show attention maps')
    
    args = parser.parse_args()
    return args


def extract_options_from_question(question_text):
    import re
    match = re.search(r'a ([^?]+?) or a ([^?]+?)\?', question_text, re.IGNORECASE)
    if match:
        return f"{match.group(1).strip()} or {match.group(2).strip()}"
    return None


def create_single_question_dataset(question, args, policy_tokenizer):
    """Create dataset for a single question"""
    original_question = question['text'].replace('<location> ', '')
    options = extract_options_from_question(original_question)
    important_line = f"\nIMPORTANT: You must instruct the vision-language model to make a selection from the available options : {options}. \n" if options else ""
    if args.enable_thinking:
        prompt = f"""<think>
    I need to optimize this question for better visual understanding. The original question is: "{original_question}"

    To refine this question for a vision-language model, I will consider the following:

    1. What are the key regions or objects in the image that should be focused on?
    2. How can I describe the visual attributes (color, shape, texture, size) that will help direct attention to these key regions?
    3. Are there any contextual or spatial clues that will aid in distinguishing between different parts of the image?
    4. How can I avoid vague or ambiguous language, ensuring the question is clear and actionable for visual analysis?
    5. If the question involves comparisons, how should I highlight the relevant visual features to help the model make a confident decision?

    After reflecting on the above points, I will now provide an optimized version of the question that effectively guides visual attention and supports the model in answering the question accurately.

    And I will provide the optimized version in the format of <answer> and </answer>.
    </think>
    """

    else:
        prompt = f"""You are an expert in optimizing questions for vision-language models.

    Your task is to rewrite the following question so that it can be better understood and answered by the model, focusing on clarity, specificity, and visual grounding.

    Original question: "{original_question}"

    Optimization guidelines:
    - Identify the most important objects or regions in the image and make them explicit.
    - Use clear and descriptive language to highlight key visual features such as color, shape, size, and texture.
    - Provide any relevant context or spatial relations that can help the model identify the target area.
    - Avoid vague or ambiguous language that could confuse the model.
    - If the question involves comparing options, guide the model to focus on distinguishing visual features between the options.

    Please provide ONLY the optimized question, wrapped in <answer> and </answer> tags, with no additional commentary.
    """
    messages = [{"role": "user", "content": prompt}]
    text = policy_tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True,
        enable_thinking=args.enable_thinking
    )
    dataset_data = [{
        "prompt": text,
        "original_question": original_question,
        "question_id": question['id'],
        "image_path": question['image_path'],
        "name": question['name'],
        "bbox": question['bbox'],
        "seg_mask": question['seg_mask'],
        "center_point": question['center_point'],
        "scribble": question['scribble'],
        "label": question['name'],
    }]
    logger.debug(f"[DEBUG] Created dataset for question {question['id']} with {len(dataset_data)} samples")
    dataset = Dataset.from_list(dataset_data)
    logger.debug(f"[DEBUG] Dataset size: {len(dataset)}")
    logger.debug(f"[DEBUG] Dataset columns: {dataset.column_names}")
    return dataset


def compute_roc_loss_with_policy(completion, qid, image_path, text, name, bbox, seg_mask, center_point, scribble, label,
                                llava_model, llava_processor, device, args):
    """Compute loss using the policy-optimized prompt"""
    
    # logger.debug(f"[DEBUG] Computing loss for question {qid}")
    logger.debug(f"[DEBUG] Completion: {completion}")
    
    import re

    where_from = "answer"
    matches = list(re.finditer(r"<answer>(.*?)</answer>", completion, re.DOTALL))
    if matches:
        optimized_question = matches[-1].group(1).strip()
    else:
        if "Optimized:" in completion:
            optimized_question = completion.split("Optimized:")[-1].strip()
            where_from = "optimized"
        else:
            optimized_question = completion.strip()
            where_from = "original"
    
    logger.debug(f"[DEBUG] Optimized question: {optimized_question} from {where_from}")
    
     # 如果提取的问题为空，使用原始问题
    if not optimized_question or optimized_question.strip() == "":
        optimized_question = text
        logger.debug(f"[DEBUG] Empty optimized question, using original: {optimized_question}")
        
    # Load and process image
    image_path_full = os.path.join(args.data_path, 'image', image_path.split('/')[-2], image_path.split('/')[-1])
    image = Image.open(image_path_full)
    iw, ih = image.size
    
    # logger.debug(f"[DEBUG] Image loaded: {image_path_full}, size: {image.size}")
    
    # Create prompt with optimized question
    prompt = f"USER: <image>\n{optimized_question} ASSISTANT:"
    
    # 清除之前的缓存
    get_local.clear()
    
    inputs = llava_processor(text=prompt, images=image, return_tensors="pt").to(device)
    # 获取图像token索引
    img_token_positions = torch.where(inputs['input_ids'] == 32000)
    
    if len(img_token_positions[0]) > 0:
        # 确保有匹配的元素
        if img_token_positions[1].numel() > 0:
            img_token_idx = img_token_positions[1][0].item()  # 使用.item()转换标量tensor
            logger.debug(f"[DEBUG] Found image token at index: {img_token_idx}")
        else:
            img_token_idx = 1
            logger.warning(f"[WARNING] No image token found in positions, using default index {img_token_idx}")
    else:
        # 如果没有找到图像token，使用默认值
        logger.error(f"[ERROR] No image token found for question {qid}")
        sys.exit(1)
    
    # logger.debug(f"[DEBUG] Image token index: {img_token_idx}")
    
    # Create mask based on visual prompt type
    mask = np.zeros((ih, iw))
    
    if args.visual_prompt == 'Box':
        x_min, y_min, x_max, y_max = int(bbox[0]), int(bbox[1]), int(bbox[0] + bbox[2]), int(bbox[1] + bbox[3])
        mask[y_min: y_max, x_min: x_max] = 1
        mask = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.CenterCrop(args.n_px),
            transforms.Resize(args.H, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor(),
        ])(mask)[0]
        logger.debug(f"[DEBUG] Box mask created: bbox={bbox}")
    
    elif args.visual_prompt == 'Mask':
        mask_path = os.path.join(args.data_path, 'mask', seg_mask)
        mask = Image.open(mask_path)
        logger.debug(f"[DEBUG][Mask] mask sum after load: {np.array(mask).sum()}, dtype: {np.array(mask).dtype}, shape: {np.array(mask).shape}")
        mask = transforms.Compose([
            transforms.Resize(args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.CenterCrop(args.n_px),
            transforms.Resize(args.H, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor(),
        ])(mask)[0]
        logger.debug(f"[DEBUG][Mask] mask sum after transform: {mask.sum()}, dtype: {mask.dtype}, shape: {mask.shape}")
        logger.debug(f"[DEBUG] Mask loaded: {mask_path}")
    
    elif args.visual_prompt == 'Scribble':
        for scri in scribble:
            mask[int(scri[1]), int(scri[0])] = 1
        mask = ((1-mask) * 255).astype(np.uint8)
        distance_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        distance_transform_normalized = cv2.normalize(distance_transform, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        mask = distance_transform_normalized
        mask = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.CenterCrop(args.n_px),
            transforms.Resize(args.H, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor(),
        ])(mask)[0]
        logger.debug(f"[DEBUG] Scribble mask created: {len(scribble)} points")
    
    elif args.visual_prompt == 'Point':
        mask[int(center_point[1]), int(center_point[0])] = 1
        mask = ((1-mask) * 255).astype(np.uint8)
        distance_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        distance_transform_normalized = cv2.normalize(distance_transform, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        mask = distance_transform_normalized
        mask = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.CenterCrop(args.n_px),
            transforms.Resize(args.H, interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor(),
        ])(mask)[0]
        logger.debug(f"[DEBUG] Point mask created: center={center_point}")
    
    mask = mask.cuda() if torch.cuda.is_available() else mask
    
    # Generate with LLaVA model
    with torch.no_grad():
        get_local.clear()
        outputs = llava_model.generate(
            **inputs,
            max_new_tokens=30,
            return_dict_in_generate=True,
            output_scores=True,
            do_sample=True,
            output_attentions=True,
            pad_token_id=llava_processor.tokenizer.eos_token_id
        )
        # 从GenerateDecoderOnlyOutput中提取sequences
        if hasattr(outputs, 'sequences'):
            generated_sequences = outputs.sequences
            decoded_output = llava_processor.batch_decode(generated_sequences, skip_special_tokens=True)[0]
            logger.debug(f"[DEBUG] Decoded output: {decoded_output}")
        else:
            logger.debug("[DEBUG] outputs has no sequences, attributes:", dir(outputs))
        
        # # 检查get_local缓存
        # logger.debug(f"[DEBUG] Available cache keys: {list(get_local.cache.keys())}")
        # logger.debug(f"[DEBUG] Cache keys details: {[(k, type(v)) for k, v in get_local.cache.items()]}")
        
        # # 检查get_local状态
        # logger.debug(f"[DEBUG] get_local.is_activate: {get_local.is_activate}")
        # logger.debug(f"[DEBUG] get_local.cache type: {type(get_local.cache)}")
        # logger.debug(f"[DEBUG] get_local.cache length: {len(get_local.cache)}")
        
        # 使用与原始代码相同的方式获取注意力图
        # logger.debug(f"[DEBUG] All cache keys: {list(get_local.cache.keys())}")
        
        # 尝试获取注意力图，使用实际的cache key
        attention_keys = ['force_eager_attention_with_decorator.<locals>.decorated_eager_attention_forward', 'eager_attention_forward']
        ori_attention_maps = None
        
        for key in attention_keys:
            if key in get_local.cache:
                ori_attention_maps = get_local.cache[key]
                # logger.debug(f"[DEBUG] Found attention maps in cache key: {key}")
                # logger.debug(f"[DEBUG] Attention maps type: {type(ori_attention_maps)}")
                if isinstance(ori_attention_maps, list):
                    # logger.debug(f"[DEBUG] Number of attention maps: {len(ori_attention_maps)}")
                    valid_maps = 0
                    for i, att in enumerate(ori_attention_maps):
                        if att is not None:
                            # logger.debug(f"[DEBUG] Attention map {i} shape: {att.shape}")
                            valid_maps += 1
                        else:
                            logger.debug(f"[DEBUG] Attention map {i} is None")
                    logger.debug(f"[DEBUG] Valid attention maps: {valid_maps}/{len(ori_attention_maps)}")
                break
        
        if ori_attention_maps is None:
            logger.error(f"[ERROR] No attention maps found in cache")
            logger.debug(f"[DEBUG] Available cache keys: {list(get_local.cache.keys())}")
            logger.debug(f"[DEBUG] Cache contents: {[(k, len(v) if isinstance(v, list) else type(v)) for k, v in get_local.cache.items()]}")
            sys.exit(1)
        
        # 处理attention maps
        attention_maps = [att for i, att in enumerate(ori_attention_maps) if att is not None and att.shape[-2] > 1]
        # logger.debug(f"[DEBUG] Found {len(attention_maps)} valid attention maps (filtered from {len(ori_attention_maps)})")
        
        if len(attention_maps) > 0:
            mean_att = torch.cat([att.to(device) for att in attention_maps], 0).mean(0)
            # logger.debug(f"[DEBUG] Mean attention shape: {mean_att.shape}")
        else:
            logger.error(f"[ERROR] No valid attention maps found")
            sys.exit(1)
        
        target2img_rel = mean_att[:, img_token_idx + args.H * args.W:, img_token_idx:img_token_idx + args.H * args.W].mean(axis=0).mean(axis=0).unsqueeze(0)
        
        if args.show_att:
            fig = show_image_relevance(
                mean_att[:, img_token_idx + args.H * args.W:, img_token_idx:img_token_idx + args.H * args.W].mean(axis=0).mean(axis=0),
                image,
                orig_image=image,
                mask=mask,
                preprocess=preprocess,
                only_map=True,
                show_mask=True
            )
            fig.savefig(f'vis/img_tmp_{qid}.png', dpi=300, bbox_inches='tight')
        
        loss = args.alpha * compute_ca_loss(target2img_rel.to(mask.device), masks=[mask], choice=args.visual_prompt, object_positions=None)
        
        logger.debug(f"[DEBUG] Loss computed: {loss.item()}")
        
        get_local.clear()
        torch.cuda.empty_cache()
        
        if (target2img_rel.reshape(-1).sum(dim=-1) == 0).any():
            logger.warning("Warning: attention map sum is zero!")
        if (mask.sum() == 0):
            logger.warning("Warning: mask sum is zero!")
        
        if torch.isnan(loss).any() or torch.isinf(loss).any():
            logger.error("Loss is nan/inf!", loss)
            return 0.0
        
        return -loss.item()


def train_policy_for_single_question(question, policy_model, policy_tokenizer, llava_model, llava_processor, device, args):
    """Train policy model for a single question using test-time GRPO"""
    
    logger.info(f"[INFO] Starting test-time GRPO for question {question['id']}")
    
    dataset = create_single_question_dataset(question, args, policy_tokenizer)
    
    def custom_reward_function(completions, prompts, **kwargs):
        logger.debug(f"[DEBUG] Computing rewards for {len(completions)} completions")
        rewards = []
        for i, completion in enumerate(completions):
            reward = compute_roc_loss_with_policy(
                completion, 
                question['id'],
                question['image_path'],
                question['text'].replace('<location> ', ''),
                question['name'],
                question['bbox'],
                question['seg_mask'],
                question['center_point'],
                question['scribble'],
                question['name'],
                llava_model,
                llava_processor,
                device,
                args
            )
            rewards.append(reward)
            logger.debug(f"[DEBUG] Reward for completion {i}: {reward}")
        return rewards
    
    # reward_func = partial(
    #     custom_reward_function,
    #     question=question,
    #     llava_model=llava_model,
    #     llava_processor=llava_processor,
    #     device=device,
    #     args=args
    # )

    if args.enable_thinking:
        temperature = 0.6
        top_p = 0.95
        top_k = 20
        min_p = 0
    else:
        temperature = 0.7
        top_p = 0.8
        top_k = 20
        min_p = 0

    # GRPO configuration for single question
    grpo_config = GRPOConfig(
        learning_rate=args.lr,
        num_generations=args.num_generations,
        max_completion_length=args.max_completion_length,
        epsilon=args.epsilon,
        per_device_train_batch_size=args.num_generations,
        gradient_accumulation_steps=1,
        max_steps=args.T, 
        logging_steps=1,
        warmup_steps=0,
        remove_unused_columns=False,
        disable_dropout=True,
        temperature=temperature,
        top_p=top_p,
        top_k=top_k,
        min_p=min_p,
    )
    
    # Initialize GRPO trainer for this question
    trainer = GRPOTrainer(
        model=policy_model,
        args=grpo_config,
        train_dataset=dataset,
        processing_class=policy_tokenizer, 
        reward_funcs=custom_reward_function,
    )
    
    logger.info(f"[INFO] Training policy model for question {question['id']}...")
    # for name, param in policy_model.named_parameters():
    #     if "embed_tokens" in name:
    #         param.requires_grad = False
    #     if torch.isnan(param).any():
    #         logger.error(f"[ERROR] Parameter {name} has nan after training!")
    #         exit(1)
    #     if torch.isinf(param).any():
    #         logger.error(f"[ERROR] Parameter {name} has inf after training!")
    #         exit(1)
    trainer.train()
    for name, param in policy_model.named_parameters():
        if torch.isnan(param).any():
            logger.error(f"[ERROR] Parameter {name} has nan after training!")
            exit(1)
        if torch.isinf(param).any():
            logger.error(f"[ERROR] Parameter {name} has inf after training!")
            exit(1)
    del policy_model
    torch.cuda.empty_cache()
    import gc
    gc.collect()

    return trainer


def generate_optimized_prompt(question, trained_policy_model, policy_tokenizer, device, args):
    """Generate optimized prompt using the trained policy model"""
    original_question = question['text'].replace('<location> ', '')
    options = extract_options_from_question(original_question)
    important_line = f"\nIMPORTANT: You must instruct the vision-language model to make a selection from the available options : {options}. \n" if options else ""
    # if args.enable_thinking:
    #     prompt = f"""<think>
    # I need to optimize this question for better visual understanding. The original question is: "{original_question}"

    # To refine this question for a vision-language model, I will consider the following:

    # 1. What are the key regions or objects in the image that should be focused on?
    # 2. How can I describe the visual attributes (color, shape, texture, size) that will help direct attention to these key regions?
    # 3. Are there any contextual or spatial clues that will aid in distinguishing between different parts of the image?
    # 4. How can I avoid vague or ambiguous language, ensuring the question is clear and actionable for visual analysis?
    # 5. If the question involves comparisons, how should I highlight the relevant visual features to help the model make a confident decision?

    # After reflecting on the above points, I will now provide an optimized version of the question that effectively guides visual attention and supports the model in answering the question accurately.

    # And I will provide the optimized version in the format of <answer> and </answer>.
    # </think>
    # """

    # else:
    #     prompt = f"""You are an expert in optimizing questions for vision-language models.

    # Your task is to rewrite the following question so that it can be better understood and answered by the model, focusing on clarity, specificity, and visual grounding.

    # Original question: "{original_question}"

    # Optimization guidelines:
    # - Identify the most important objects or regions in the image and make them explicit.
    # - Use clear and descriptive language to highlight key visual features such as color, shape, size, and texture.
    # - Provide any relevant context or spatial relations that can help the model identify the target area.
    # - Avoid vague or ambiguous language that could confuse the model.
    # - If the question involves comparing options, guide the model to focus on distinguishing visual features between the options.

    # Please provide ONLY the optimized question, wrapped in <answer> and </answer> tags, with no additional commentary.
    # """
    # 新版prompt工程
    location_caution = "- Do NOT assume the location of the object or region in the image (e.g., 'center', 'main', 'middle') unless explicitly mentioned in the original question."
    option_caution = "- When rewriting the question, you must keep the original option words (e.g., 'watch', 'helmet') in the options, and preserve their order. Do NOT replace them with only descriptive phrases."
    compare_example = "- Example: 'Is the object a watch, which is a circular timekeeping device, or a helmet, which is a protective headgear?'"
    if args.enable_thinking:
        prompt = f"""<think>
You are an expert in optimizing questions for vision-language models.

Your task is to rewrite the following question so that it can be better understood and answered by the model, focusing on clarity, specificity, and visual grounding.

Original question: \"{original_question}\"

Optimization guidelines:
1. Identify the most important objects or regions in the image and make them explicit in the question.
2. Use clear and descriptive language to highlight key visual features, prioritizing: color > shape > material/texture > size > spatial relations.
3. Provide any relevant context or spatial relations that can help the model identify the target area, but {location_caution}
4. Avoid vague or ambiguous language that could confuse the model.
5. If the question involves comparing options:
   - For each option, keep the original option word exactly as in the original question.
   - Add distinguishing visual features as modifiers, but do NOT replace the option word with only a description.
   - {compare_example}
6. Preserve the original order of options.
7. Do NOT add any explanations, background information, or commentary—just rewrite the question itself.
8. The output should be a single, concise question, wrapped in <answer> and </answer> tags, with no additional text.

After reflecting on the above points, I will now provide an optimized version of the question that effectively guides visual attention and supports the model in answering the question accurately.

And I will provide the optimized version in the format of <answer> and </answer>.
</think>
"""
    else:
        prompt = f"""You are an expert in optimizing questions for vision-language models.

Your task is to rewrite the following question so that it can be better understood and answered by the model, focusing on clarity, specificity, and visual grounding.

Original question: \"{original_question}\"

Optimization guidelines:
1. Identify the most important objects or regions in the image and make them explicit in the question.
2. Use clear and descriptive language to highlight key visual features, prioritizing: color > shape > material/texture > size > spatial relations.
3. Provide any relevant context or spatial relations that can help the model identify the target area, but {location_caution}
4. Avoid vague or ambiguous language that could confuse the model.
5. If the question involves comparing options:
   - For each option, keep the original option word exactly as in the original question.
   - Add distinguishing visual features as modifiers, but do NOT replace the option word with only a description.
   - {compare_example}
6. Preserve the original order of options.
7. Do NOT add any explanations, background information, or commentary—just rewrite the question itself.
8. The output should be a single, concise question, wrapped in <answer> and </answer> tags, with no additional text.

Please provide ONLY the optimized question, wrapped in <answer> and </answer> tags, with no additional commentary.
"""
    messages = [{"role": "user", "content": prompt}]
    text = policy_tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True,
        enable_thinking=args.enable_thinking
    )
    inputs = policy_tokenizer([text], return_tensors="pt").to(device)
    # logger.debug("[DEBUG] Calling policy model generate...")
    # logger.debug(f"pad_token: {policy_tokenizer.pad_token}, pad_token_id: {policy_tokenizer.pad_token_id}")
    # logger.debug(f"eos_token: {policy_tokenizer.eos_token}, eos_token_id: {policy_tokenizer.eos_token_id}")
    # logger.debug(f"inputs['input_ids']: {inputs['input_ids']}")
    if args.enable_thinking:
        temperature = 0.6
        top_p = 0.95
        top_k = 20
        min_p = 0
    else:
        temperature = 0.7
        top_p = 0.8
        top_k = 20
        min_p = 0

    with torch.no_grad():
        outputs = trained_policy_model.generate(
            **inputs,
            max_new_tokens=args.max_completion_length,
            do_sample=True,
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            min_p=min_p,
            pad_token_id=policy_tokenizer.eos_token_id,
            eos_token_id=policy_tokenizer.eos_token_id,
            bos_token_id=getattr(policy_tokenizer, 'bos_token_id', None)
        )
    optimized_prompt = policy_tokenizer.decode(outputs[0])
    logger.debug(f"[DEBUG] Raw policy output: {optimized_prompt}")
    import re
    matches = list(re.finditer(r"<answer>(.*?)</answer>", optimized_prompt, re.DOTALL))
    if matches:
        optimized_question = matches[-1].group(1).strip()
        logger.debug(f"[DEBUG] Extracted optimized question: {optimized_question}")
    else:
        optimized_question = original_question
        logger.debug(f"[DEBUG] No <answer> tags found, using original")
    return optimized_question


def generate_original_llava_output(question, llava_model, llava_processor, device, args):
    """Generate original LLaVA output without any optimization"""
    
    original_question = question['text'].replace('<location> ', '')
    image_path = os.path.join(args.data_path, 'image', question['image_path'].split('/')[-2], question['image_path'].split('/')[-1])
    image = Image.open(image_path)
    
    prompt = f"USER: <image>\n{original_question} ASSISTANT:"
    inputs = llava_processor(text=prompt, images=image, return_tensors="pt").to(device)
    
    with torch.no_grad():
        outputs = llava_model.generate(
            **inputs,
            max_new_tokens=30,
            do_sample=False,
            pad_token_id=llava_processor.tokenizer.eos_token_id
        )
    
    output = llava_processor.batch_decode(outputs, skip_special_tokens=True)[0]
    return output


def force_eager_attention_with_decorator(model):
    """强制将所有LlamaDecoderLayer的self_attn替换为eager实现，并添加get_local装饰器"""
    replaced = 0
    from transformers.models.llama.modeling_llama import LlamaDecoderLayer, LlamaAttention, eager_attention_forward
    
    original_eager_attention_forward = eager_attention_forward
    
    @get_local('attn_weights')
    def decorated_eager_attention_forward(module, query, key, value, attention_mask, scaling, dropout=0.0, **kwargs):
        key_states = repeat_kv(key, module.num_key_value_groups)
        value_states = repeat_kv(value, module.num_key_value_groups)

        attn_weights = torch.matmul(query, key_states.transpose(2, 3)) * scaling
        if attention_mask is not None:
            causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]
            attn_weights = attn_weights + causal_mask

        attn_weights = nn.functional.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query.dtype)
        attn_weights = nn.functional.dropout(attn_weights, p=dropout, training=module.training)
        attn_output = torch.matmul(attn_weights, value_states)
        attn_output = attn_output.transpose(1, 2).contiguous()

        # 添加调试信息
        # logger.debug(f"[DEBUG] eager_attention_forward called, attn_weights shape: {attn_weights.shape}")
        
        return attn_output, attn_weights
    
    # 设置正确的__qualname__
    decorated_eager_attention_forward.__qualname__ = 'eager_attention_forward'
    
    # 替换eager_attention_forward函数
    import transformers.models.llama.modeling_llama as llama_module
    llama_module.eager_attention_forward = decorated_eager_attention_forward
    
    # 强制使用eager实现
    for name, module in model.named_modules():
        if isinstance(module, LlamaDecoderLayer):
            # 强制设置attention实现为eager
            module.self_attn.config._attn_implementation = "eager"
            replaced += 1
    
    logger.debug(f"[DEBUG] Forced {replaced} LlamaDecoderLayer to use eager attention implementation and decorated eager_attention_forward")
    return model


def print_cuda_memory(tag=""):
    if torch.cuda.is_available():
        logger.info(f"[CUDA][{tag}] Allocated: {torch.cuda.memory_allocated() / 1024**2:.2f} MB, "
              f"Reserved: {torch.cuda.memory_reserved() / 1024**2:.2f} MB, "
              f"Max Allocated: {torch.cuda.max_memory_allocated() / 1024**2:.2f} MB, "
              f"Max Reserved: {torch.cuda.max_memory_reserved() / 1024**2:.2f} MB")

def generate_experiment_filename(args, base_filename):
    """
    Generate filename with experiment configuration information
    
    Args:
        args: parsed arguments containing experiment parameters
        base_filename: base filename without extension
    
    Returns:
        str: filename with experiment configuration
    """
    import datetime
    
    # Extract key configuration parameters
    config_parts = []
    
    # Method identifier
    config_parts.append("testtime")
    
    # Visual prompt type
    config_parts.append(f"vp{args.visual_prompt}")
    
    # Key hyperparameters
    config_parts.append(f"T{args.T}")
    config_parts.append(f"lr{args.lr}")
    config_parts.append(f"eps{args.epsilon}")
    config_parts.append(f"alpha{args.alpha}")
    config_parts.append(f"n_gen{args.num_generations}")
    
    # Thinking mode
    if args.enable_thinking:
        config_parts.append("thinking")
    
    # Timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    config_parts.append(timestamp)
    
    # Combine all parts
    config_str = "_".join(config_parts)
    
    return f"{base_filename}_{config_str}"

args = parse_args()

def main():
    
    logger.info(f"[INFO] Starting Test-time GRPO ControlMLLM")
    logger.info(f"[INFO] Visual Prompt: {args.visual_prompt}")
    logger.info(f"[INFO] T iterations per question: {args.T}")
    
    # Create vis directory if show_att is enabled
    if args.show_att:
        os.makedirs('vis', exist_ok=True)
    
    # Load LLaVA model and processor
    logger.info("[INFO] Loading LLaVA model...")
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
    )

    llava_model = LlavaForConditionalGeneration.from_pretrained(
        args.model_path,
        quantization_config=quantization_config,
        device_map="auto"
    )
    
    llava_processor = AutoProcessor.from_pretrained(args.model_path)
    llava_processor.patch_size = 14
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 确保get_local被激活
    get_local.activate()
    logger.debug(f"[DEBUG] get_local activated: {get_local.is_activate}")
    
    # 检查模型配置
    logger.debug(f"[DEBUG] Model config attention implementation: {llava_model.config._attn_implementation}")
    logger.debug(f"[DEBUG] Model config model type: {llava_model.config.model_type}")
    logger.debug(f"[DEBUG] Model config architecture: {llava_model.config.architectures}")
    
    # 强制eager实现并添加get_local装饰器
    llava_model.config._attn_implementation = "eager"
    llava_model = force_eager_attention_with_decorator(llava_model)
    
    logger.info(f"[INFO] LLaVA model loaded on {device}")
    
    # Freeze LLaVA model parameters
    for p in llava_model.parameters():
        p.requires_grad = False
    
    # Load questions
    logger.info("[INFO] Loading questions...")
    questions = [json.loads(q) for q in open(args.question_file, "r")]
    logger.info(f"[INFO] Loaded {len(questions)} questions")
    
    # 只处理前100个问题
    # questions = questions[100:200]
    
    # Process each question individually with test-time GRPO
    base, ext = os.path.splitext(os.path.expanduser(args.answers_file))
    # Generate filename with experiment configuration
    config_filename = generate_experiment_filename(args, base)
    answers_file = f"{config_filename}{ext}"
    os.makedirs(os.path.dirname(answers_file), exist_ok=True)
    
    if args.resume and os.path.exists(answers_file):
        answers = [json.loads(q) for q in open(answers_file, "r")]
        answer_ids = [q['question_id'] for q in answers]
    else:
        answer_ids = []
    
    if args.resume:
        ans_file = open(answers_file, "a")
    else:
        ans_file = open(answers_file, "w")
    
    # Load policy model (Qwen3-4B) 只加载一次
    logger.info("[INFO] Loading policy model (once)...")
    policy_tokenizer = AutoTokenizer.from_pretrained(args.policy_model_path)
    policy_model = AutoModelForCausalLM.from_pretrained(
        args.policy_model_path,
        torch_dtype=torch.float16,
        device_map="auto"
    )
    # 冻结所有参数
    for param in policy_model.parameters():
        param.requires_grad = False
    # 插入LoRA adapter
    lora_config = LoraConfig(
        r=8,
        lora_alpha=16,
        target_modules=["q_proj", "v_proj"],
        lora_dropout=0.05,
        bias="none",
        task_type=TaskType.CAUSAL_LM
    )
    policy_model = get_peft_model(policy_model, lora_config)
    policy_model.print_trainable_parameters()
    emb = policy_model.get_input_embeddings().weight.data
    logger.debug(f"Embedding stats: min={emb.min().item()}, max={emb.max().item()}, mean={emb.mean().item()}, nan={torch.isnan(emb).any().item()}, inf={torch.isinf(emb).any().item()}")
    policy_model_init_state = copy.deepcopy(policy_model.state_dict())
    
    logger.info("Processing questions with test-time GRPO...")
    answers_buffer = []
    for idx, q in enumerate(tqdm(questions, desc="Processing Questions")):
        qid = q['id']
        if args.resume and answer_ids and qid in answer_ids:
            continue
        logger.info(f"\n[INFO] Processing question {qid}")
        
        # Step 0: Generate original LLaVA output (without optimization)
        logger.info(f"[INFO] Step 0: Generating original LLaVA output for question {qid}")
        original_output = generate_original_llava_output(q, llava_model, llava_processor, device, args)
        logger.debug(f"[DEBUG] Original LLaVA output: {original_output[:100]}...")
        
        # Step 1: 每个问题优化前重置policy model参数
        logger.info(f"[INFO] Step 1: Resetting policy model parameters for question {qid}")
        policy_model.load_state_dict(policy_model_init_state)
        
        # Step 2: Train policy model for this specific question
        logger.info(f"[INFO] Step 2: Training policy model for question {qid}")
        trained_policy_model = train_policy_for_single_question(
            q, policy_model, policy_tokenizer, llava_model, llava_processor, device, args
        )
        
        # Step 3: Generate optimized prompt using the trained policy model
        logger.info(f"[INFO] Step 3: Generating optimized prompt for question {qid}")
        optimized_question = generate_optimized_prompt(q, trained_policy_model.model, policy_tokenizer, device, args)
        
        logger.debug(f"[DEBUG] Original question: {q['text'].replace('<location> ', '')}")
        logger.debug(f"[DEBUG] Optimized question: {optimized_question}")
        
        # Step 4: Use optimized prompt with LLaVA model for final answer
        logger.info(f"[INFO] Step 4: Generating optimized LLaVA output for question {qid}")
        image_path = os.path.join(args.data_path, 'image', q['image_path'].split('/')[-2], q['image_path'].split('/')[-1])
        image = Image.open(image_path)
        logger.info(image)
        prompt = f"USER: <image>\n{optimized_question} ASSISTANT:"
        inputs = llava_processor(text=prompt, images=image, return_tensors="pt").to(device)
        
        with torch.no_grad():
            outputs = llava_model.generate(
                **inputs,
                max_new_tokens=30,
                do_sample=False,
                pad_token_id=llava_processor.tokenizer.eos_token_id
            )
        
        optimized_output = llava_processor.batch_decode(outputs, skip_special_tokens=True)[0]
        logger.debug(f"[DEBUG] Optimized LLaVA output: {optimized_output}")
        
        # Create output_T list similar to original llava_roc.py
        output_T = [original_output, optimized_output]  # First: original, Second: optimized
        
        # Save results with output_T format
        answers_buffer.append({
            "question_id": qid,
            "answers": output_T,  # List with [original_output, optimized_output]
            "relevancy": [[0, 0], [0, 0]],  # Placeholder relevancy scores
            "label": q['name']
        })
        # 每10个问题写入一次
        if len(answers_buffer) >= 3:
            for ans in answers_buffer:
                ans_file.write(json.dumps(ans) + '\n')
            ans_file.flush()
            answers_buffer = []
    # 处理剩余未写入的
    if answers_buffer:
        for ans in answers_buffer:
            ans_file.write(json.dumps(ans) + '\n')
        ans_file.flush()
    
    ans_file.close()
    logger.info("Processing completed!")


if __name__ == "__main__":
    import datetime
    # 日志配置
    base, ext = os.path.splitext(os.path.expanduser(args.answers_file))
    thinking_tag = "enable_thinking" if args.enable_thinking else "no_thinking"
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = os.path.join("logs", args.visual_prompt)
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(
        log_dir,
        f"llava_roc_testtime_debug_{args.visual_prompt}_{thinking_tag}_{timestamp}.log"
    )
    logging.basicConfig(
        filename=log_path,
        filemode='a',
        format='%(asctime)s %(levelname)s: %(message)s',
        level=logging.DEBUG
    )
    logger = logging.getLogger(__name__)
    main() 