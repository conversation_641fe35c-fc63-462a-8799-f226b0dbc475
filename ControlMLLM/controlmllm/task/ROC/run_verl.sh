#!/bin/bash

# VERL Test-time GRPO ControlMLLM for ROC dataset
# This script runs the VERL-based optimization for visual question answering

# Configuration 1: VERL GRPO with resume functionality
export CUDA_VISIBLE_DEVICES=4,5
export TOKENIZERS_PARALLELISM=false
python llava_roc_verl.py \
    --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
    --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
    --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
    --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
    --visual_prompt "Box" \
    --answers_file "outputs/llava_roc_verl_nothinking_4B_4T_showatt.json" \
    --T 4 \
    --num_generations 4 \
    --max_completion_length 128 \
    --lr 1e-6 \
    --epsilon 0.1 \
    --alpha 400 \
    --entropy_weight 0.1 \
    --temperature 0.7 \
    --top_p 0.8 \
    --top_k 20 \
    --resume \
    --show_att

# export CUDA_VISIBLE_DEVICES=6,7
# export TOKENIZERS_PARALLELISM=false
# python llava_roc_verl.py \
#     --model_path "/data1/mrwu_tmp/haoyuan/models/llava-hf/llava-1.5-7b-hf" \
#     --policy_model_path "/data1/mrwu_tmp/haoyuan/models/Qwen/Qwen3-4B" \
#     --data_path "/data1/mrwu_tmp/haoyuan/data/ROC/LVIS" \
#     --question_file "/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json" \
#     --visual_prompt "Box" \
#     --answers_file "outputs/llava_roc_verl_thinking_4B_4T.json" \
#     --T 4 \
#     --num_generations 4 \
#     --max_completion_length 1024 \
#     --lr 1e-6 \
#     --epsilon 0.1 \
#     --alpha 400 \
#     --entropy_weight 0.1 \
#     --temperature 0.6 \
#     --top_p 0.95 \
#     --top_k 20 \
#     --resume \
#     --enable_thinking

# echo "VERL Test-time GRPO ControlMLLM script completed!" 