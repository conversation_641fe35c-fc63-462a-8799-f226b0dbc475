import os, sys
sys.setrecursionlimit(20000)
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from visualizer import get_local
get_local.activate()
import argparse
from PIL import Image
import torch
import gc
from transformers import AutoProcessor, LlavaForConditionalGeneration, AutoTokenizer, AutoModelForCausalLM
import torchvision.transforms as transforms
import numpy as np
import json
import cv2
from tqdm import tqdm
import torch.nn.functional as F
import logging
from utils import show_image_relevance
import torch.nn as nn
import clip
_, preprocess = clip.load("ViT-B/32", device='cpu', jit=False)
def parse_args():
    parser = argparse.ArgumentParser(description="Test-time LLM-only ControlMLLM")
    parser.add_argument('--model_path', type=str, default="pretrained_models/llava-1.5-7b-hf", help='Path to the pretrained model')
    parser.add_argument('--policy_model_path', type=str, default="Qwen/Qwen2.5-4B-Instruct", help='Path to the policy model')
    parser.add_argument('--data_path', type=str, default="data/ROC/LVIS", help='Path to the dataset')
    parser.add_argument('--question_file', type=str, default='data/ROC/question_roc.json', help='Path to the question file')
    parser.add_argument('--answers_file', type=str, default='outputs/llava_roc_testtime_llm_only.json', help='Path to the answers file')
    parser.add_argument('--visual_prompt', type=str, default='Box', help='Visual prompt method')
    parser.add_argument('--H', type=int, default=24, help='Height of the attention map')
    parser.add_argument('--W', type=int, default=24, help='Width of the attention map')
    parser.add_argument('--n_px', type=int, default=224, help='Input size of image')
    parser.add_argument('--enable_thinking', action='store_true', help='Enable Qwen3 thinking mode in prompt')
    parser.add_argument('--resume', action='store_true', help='Resume from checkpoint')
    parser.add_argument('--show_att', action='store_true', help='Flag to show attention maps')
    args = parser.parse_args()
    return args

def extract_options_from_question(question_text):
    import re
    match = re.search(r'a ([^?]+?) or a ([^?]+?)\?', question_text, re.IGNORECASE)
    if match:
        return f"{match.group(1).strip()} or {match.group(2).strip()}"
    return None

def build_optimized_prompt(question, args, policy_tokenizer):
    original_question = question['text'].replace('<location> ', '')
    options = extract_options_from_question(original_question)
    # 新版prompt工程
    location_caution = "- Do NOT assume the location of the object or region in the image (e.g., 'center', 'main', 'middle') unless explicitly mentioned in the original question."
    option_caution = "- When rewriting the question, you must keep the original option words (e.g., 'watch', 'helmet') in the options, and preserve their order. Do NOT replace them with only descriptive phrases."
    compare_example = "- Example: 'Is the object a watch, which is a circular timekeeping device, or a helmet, which is a protective headgear?'"
    if args.enable_thinking:
        prompt = f"""<think>
You are an expert in optimizing questions for vision-language models.

Your task is to rewrite the following question so that it can be better understood and answered by the model, focusing on clarity, specificity, and visual grounding.

Original question: \"{original_question}\"

Optimization guidelines:
1. Identify the most important objects or regions in the image and make them explicit in the question.
2. Use clear and descriptive language to highlight key visual features, prioritizing: color > shape > material/texture > size > spatial relations.
3. Provide any relevant context or spatial relations that can help the model identify the target area, but {location_caution}
4. Avoid vague or ambiguous language that could confuse the model.
5. If the question involves comparing options:
   - For each option, keep the original option word exactly as in the original question.
   - Add distinguishing visual features as modifiers, but do NOT replace the option word with only a description.
   - {compare_example}
6. Preserve the original order of options.
7. Do NOT add any explanations, background information, or commentary—just rewrite the question itself.
8. The output should be a single, concise question, wrapped in <answer> and </answer> tags, with no additional text.

After reflecting on the above points, I will now provide an optimized version of the question that effectively guides visual attention and supports the model in answering the question accurately.

And I will provide the optimized version in the format of <answer> and </answer>.
</think>
"""
    else:
        prompt = f"""You are an expert in optimizing questions for vision-language models.

Your task is to rewrite the following question so that it can be better understood and answered by the model, focusing on clarity, specificity, and visual grounding.

Original question: \"{original_question}\"

Optimization guidelines:
1. Identify the most important objects or regions in the image and make them explicit in the question.
2. Use clear and descriptive language to highlight key visual features, prioritizing: color > shape > material/texture > size > spatial relations.
3. Provide any relevant context or spatial relations that can help the model identify the target area, but {location_caution}
4. Avoid vague or ambiguous language that could confuse the model.
5. If the question involves comparing options:
   - For each option, keep the original option word exactly as in the original question.
   - Add distinguishing visual features as modifiers, but do NOT replace the option word with only a description.
   - {compare_example}
6. Preserve the original order of options.
7. Do NOT add any explanations, background information, or commentary—just rewrite the question itself.
8. The output should be a single, concise question, wrapped in <answer> and </answer> tags, with no additional text.

Please provide ONLY the optimized question, wrapped in <answer> and </answer> tags, with no additional commentary.
"""
    messages = [{"role": "user", "content": prompt}]
    text = policy_tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True,
        enable_thinking=args.enable_thinking
    )
    return text, original_question

def generate_optimized_question(policy_model, policy_tokenizer, prompt_text, args, device):
    # print("\n[DEBUG] ==== Optimized Prompt Input ====\n", prompt_text)
    inputs = policy_tokenizer([prompt_text], return_tensors="pt").to(device)
    if args.enable_thinking:
        temperature = 0.6
        top_p = 0.95
        top_k = 20
        min_p = 0
    else:
        temperature = 0.7
        top_p = 0.8
        top_k = 20
        min_p = 0
    with torch.no_grad():
        outputs = policy_model.generate(
            **inputs,
            max_new_tokens=1024,
            do_sample=True,
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            min_p=min_p,
            pad_token_id=policy_tokenizer.eos_token_id,
            eos_token_id=policy_tokenizer.eos_token_id,
            bos_token_id=getattr(policy_tokenizer, 'bos_token_id', None)
        )
    decoded = policy_tokenizer.decode(outputs[0])
    # print("[DEBUG] ==== Policy Model Decoded Output ====\n", decoded)
    import re
    matches = list(re.finditer(r"<answer>(.*?)</answer>", decoded, re.DOTALL))
    if matches:
        optimized_question = matches[-1].group(1).strip()
        # print("[DEBUG] ==== Extracted <answer>...<\/answer> ====\n", optimized_question)
    else:
        optimized_question = decoded.strip()
        # print("[DEBUG] ==== No <answer> tag found, using full decoded ====\n", optimized_question)
    return optimized_question

def generate_llava_output(question, llava_model, llava_processor, device, args, question_text):
    image_path = os.path.join(args.data_path, 'image', question['image_path'].split('/')[-2], question['image_path'].split('/')[-1])
    image = Image.open(image_path)
    prompt = f"USER: <image>\n{question_text} ASSISTANT:"
    inputs = llava_processor(text=prompt, images=image, return_tensors="pt").to(device)
    
    # 获取图像token索引
    img_token_positions = torch.where(inputs['input_ids'] == 32000)
    if len(img_token_positions[0]) > 0 and img_token_positions[1].numel() > 0:
        img_token_idx = img_token_positions[1][0].item()
    else:
        img_token_idx = 1
    
    with torch.no_grad():
        outputs = llava_model.generate(
            **inputs,
            max_new_tokens=45,
            do_sample=False,
            output_attentions=True,
            pad_token_id=llava_processor.tokenizer.eos_token_id
        )
    
    output = llava_processor.batch_decode(outputs, skip_special_tokens=True)[0]
    
    # 如果启用了 show_att，生成注意力图
    if args.show_att:
        # 获取注意力图
        attention_keys = ['force_eager_attention_with_decorator.<locals>.decorated_eager_attention_forward', 'eager_attention_forward']
        ori_attention_maps = None
        
        for key in attention_keys:
            if key in get_local.cache:
                ori_attention_maps = get_local.cache[key]
                break
        
        if ori_attention_maps is not None:
            attention_maps = [att for att in ori_attention_maps if att is not None and att.shape[-2] > 1]
            if len(attention_maps) > 0:
                mean_att = torch.cat([att.to(device) for att in attention_maps], 0).mean(0)
                
                # 创建简单的mask用于可视化
                iw, ih = image.size
                mask = np.zeros((ih, iw))
                
                if args.visual_prompt == 'Box':
                    bbox = question['bbox']
                    x_min, y_min, x_max, y_max = int(bbox[0]), int(bbox[1]), int(bbox[0] + bbox[2]), int(bbox[1] + bbox[3])
                    mask[y_min: y_max, x_min: x_max] = 1
                    mask = transforms.Compose([
                        transforms.ToPILImage(),
                        transforms.Resize(args.n_px, interpolation=transforms.InterpolationMode.NEAREST),
                        transforms.CenterCrop(args.n_px),
                        transforms.Resize(args.H, interpolation=transforms.InterpolationMode.NEAREST),
                        transforms.ToTensor(),
                    ])(mask)[0]
                
                # 生成注意力图
                fig = show_image_relevance(
                    mean_att[:, img_token_idx + args.H * args.W:, img_token_idx:img_token_idx + args.H * args.W].mean(axis=0).mean(axis=0),
                    image,
                    orig_image=image,
                    mask=mask,
                    preprocess=preprocess,
                    only_map=True,
                    show_mask=True
                )
                fig.savefig(f'vis/img_tmp_{question["id"]}.png', dpi=300, bbox_inches='tight')
    
    return output

def generate_experiment_filename(args, base_filename):
    """
    Generate filename with experiment configuration information
    
    Args:
        args: parsed arguments containing experiment parameters
        base_filename: base filename without extension
    
    Returns:
        str: filename with experiment configuration
    """
    import datetime
    
    # Extract key configuration parameters
    config_parts = []
    
    # Method identifier
    config_parts.append("llm_only")
    
    # Visual prompt type
    config_parts.append(f"vp{args.visual_prompt}")
    
    # Thinking mode
    if args.enable_thinking:
        config_parts.append("thinking")
    
    # Timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    config_parts.append(timestamp)
    
    # Combine all parts
    config_str = "_".join(config_parts)
    
    return f"{base_filename}_{config_str}"

def main():
    args = parse_args()
    base, ext = os.path.splitext(os.path.expanduser(args.answers_file))
    # Generate filename with experiment configuration
    config_filename = generate_experiment_filename(args, base)
    answers_file = f"{config_filename}{ext}"
    os.makedirs(os.path.dirname(answers_file), exist_ok=True)
    # 日志配置
    import datetime
    thinking_tag = "enable_thinking" if args.enable_thinking else "no_thinking"
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = os.path.join("logs", args.visual_prompt)
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(
        log_dir,
        f"llava_roc_testtime_llm_only_{args.visual_prompt}_{thinking_tag}_{timestamp}.log"
    )
    logging.basicConfig(
        filename=log_path,
        filemode='a',
        format='%(asctime)s %(levelname)s: %(message)s',
        level=logging.DEBUG
    )
    logger = logging.getLogger(__name__)
    logger.info(f"[INFO] Starting LLM-only ControlMLLM")
    logger.info(f"[INFO] Visual Prompt: {args.visual_prompt}")
    
    # Create vis directory if show_att is enabled
    if args.show_att:
        os.makedirs('vis', exist_ok=True)
    # Load LLaVA model and processor
    logger.info("[INFO] Loading LLaVA model...")
    llava_model = LlavaForConditionalGeneration.from_pretrained(
        args.model_path,
        device_map="auto"
    )
    llava_processor = AutoProcessor.from_pretrained(args.model_path)
    device = "cuda" if torch.cuda.is_available() else "cpu"
    get_local.activate()
    
    # 如果启用了 show_att，需要强制使用 eager attention
    if args.show_att:
        from transformers.models.llama.modeling_llama import LlamaDecoderLayer, LlamaAttention, eager_attention_forward
        from transformers.models.llama.modeling_llama import repeat_kv, apply_rotary_pos_emb
        import torch.nn as nn
        
        @get_local('attn_weights')
        def decorated_eager_attention_forward(module, query, key, value, attention_mask, scaling, dropout=0.0, **kwargs):
            key_states = repeat_kv(key, module.num_key_value_groups)
            value_states = repeat_kv(value, module.num_key_value_groups)

            attn_weights = torch.matmul(query, key_states.transpose(2, 3)) * scaling
            if attention_mask is not None:
                causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]
                attn_weights = attn_weights + causal_mask

            attn_weights = nn.functional.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query.dtype)
            attn_weights = nn.functional.dropout(attn_weights, p=dropout, training=module.training)
            attn_output = torch.matmul(attn_weights, value_states)
            attn_output = attn_output.transpose(1, 2).contiguous()

            return attn_output, attn_weights
        
        decorated_eager_attention_forward.__qualname__ = 'eager_attention_forward'
        
        # 替换eager_attention_forward函数
        import transformers.models.llama.modeling_llama as llama_module
        llama_module.eager_attention_forward = decorated_eager_attention_forward
        
        # 强制使用eager实现
        replaced = 0
        for name, module in llava_model.named_modules():
            if isinstance(module, LlamaDecoderLayer):
                module.self_attn.config._attn_implementation = "eager"
                replaced += 1
        
        logger.debug(f"[DEBUG] Forced {replaced} LlamaDecoderLayer to use eager attention implementation")
        llava_model.config._attn_implementation = "eager"
    # Freeze LLaVA model parameters
    for p in llava_model.parameters():
        p.requires_grad = False
    # Load questions
    logger.info("[INFO] Loading questions...")
    questions = [json.loads(q) for q in open(args.question_file, "r")]
    logger.info(f"[INFO] Loaded {len(questions)} questions")
    # Load policy model (Qwen3-4B) 只加载一次
    logger.info("[INFO] Loading policy model (LLM-only)...")
    policy_tokenizer = AutoTokenizer.from_pretrained(args.policy_model_path)
    policy_model = AutoModelForCausalLM.from_pretrained(
        args.policy_model_path,
        torch_dtype=torch.float16,
        device_map="auto"
    )
    for param in policy_model.parameters():
        param.requires_grad = False
    # Resume logic
    if args.resume and os.path.exists(answers_file):
        answers = [json.loads(q) for q in open(answers_file, "r")]
        answer_ids = [q['question_id'] for q in answers]
    else:
        answer_ids = []
    if args.resume:
        ans_file = open(answers_file, "a")
    else:
        ans_file = open(answers_file, "w")
    answers_buffer = []
    for idx, q in enumerate(tqdm(questions, desc="Processing Questions")):
        qid = q['id']
        if args.resume and answer_ids and qid in answer_ids:
            continue
        logger.info(f"\n[INFO] Processing question {qid}")
        # Step 1: 生成优化后的问题
        prompt_text, original_question = build_optimized_prompt(q, args, policy_tokenizer)
        # print(f"[DEBUG] [QID={qid}] Prompt text for policy model:\n", prompt_text)
        optimized_question = generate_optimized_question(policy_model, policy_tokenizer, prompt_text, args, device)
        # logger.debug(f"[DEBUG] Original question: {original_question}")
        # logger.debug(f"[DEBUG] Optimized question: {optimized_question}")
        # Step 2: 用原始和优化后的问题分别推理
        original_output = generate_llava_output(q, llava_model, llava_processor, device, args, original_question)
        optimized_output = generate_llava_output(q, llava_model, llava_processor, device, args, optimized_question)
        output_T = [original_output, optimized_output]
        answers_buffer.append({
            "question_id": qid,
            "answers": output_T,
            "relevancy": [[0, 0], [0, 0]],
            "label": q['name']
        })
        if len(answers_buffer) >= 3:
            for ans in answers_buffer:
                ans_file.write(json.dumps(ans) + '\n')
            ans_file.flush()
            answers_buffer = []
    if answers_buffer:
        for ans in answers_buffer:
            ans_file.write(json.dumps(ans) + '\n')
        ans_file.flush()
    ans_file.close()
    logger.info("Processing completed!")

if __name__ == "__main__":
    main() 