"""
Usage:
- Eval Prediction:
python task/ROC/eval.py --pred_file=[your generated result by running task/ROC/llava_roc.sh] --set='test'


python eval.py --pred_file=/data1/mrwu_tmp/haoyuan/ControlMLLM/controlmllm/task/ROC/outputs/llava_roc_verl_nothinking_4B_4T_showatt_Box.json --show-switch-cnt --ann_file=/data1/mrwu_tmp/haoyuan/data/ROC/question_roc.json 
"""

import argparse
import json
import os
import re
import random
from tqdm import tqdm

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--ann_file', type=str, default='data/ROC/question_roc.json')
    parser.add_argument('--pred_file', type=str, default='outputs/llava_7b_roc_box.json')
    parser.add_argument('--set', type=str, default='test', choices=['test', 'val'])
    parser.add_argument('--show-baseline-only-miss', action='store_true', help='显示原始对但优化错的case')
    parser.add_argument('--show-ours-only-hit', action='store_true', help='显示优化对但原始错的case')
    parser.add_argument('--show-switch-cnt', action='store_true', help='显示上述两种case的数量')
    return parser.parse_args()

def remove_not_phrases_v2(text):
    # This function seems unused in the final logic but kept for historical context
    pattern = r"\s+not[^,.]*[,.]"
    text = re.sub(pattern, "", text)
    pattern = r"\s+no[^,.]*[,.]"
    text = re.sub(pattern, "", text)
    return text 

def normalize_text(text):
    return text.replace('_', ' ').lower().strip()

def extract_assistant_first_sentence(text):
    # This function is effectively superseded by the logic within extract_is_a_phrase
    if "ASSISTANT:" in text:
        content = text.split("ASSISTANT:", 1)[1].strip()
        match = re.match(r"(.+?[.!?])", content)
        if match:
            return match.group(1).strip()
        return content.split("\n")[0].strip() 
    return text

def extract_is_a_phrase(text):
    if "ASSISTANT:" in text:
        content = text.split("ASSISTANT:", 1)[1].strip()
    else:
        content = text.strip()
    first_sentence = re.split(r'[.?!\n]', content, maxsplit=1)[0]
    
    # 改进的正则表达式，能更好地处理复杂描述
    # 1. 匹配 "is a [answer], which is [description]" 格式
    match = re.search(
        r'is a[n]? ([^,]+?)(?:, which is|, that is|, a|, an|, the|, not|, rather than|, except|, other than)',
        first_sentence, re.IGNORECASE)
    if match:
        phrase = match.group(1).strip()
        return f'is a {phrase}'
    
    # 2. 匹配 "is a [answer]" 格式（简单情况）
    match = re.search(
        r'is a[n]? ([^.;\n]+?)(?=[.;\n]|$)',
        first_sentence, re.IGNORECASE)
    if match:
        phrase = match.group(1).strip()
        if ',' in phrase:
            phrase = phrase.split(',')[0].strip()
        return f'is a {phrase}'
    
    # 3. 匹配 "is [answer]" 格式（没有冠词）
    match = re.search(
        r'is ([^,.;\n]+?)(?:, which is|, that is|, a|, an|, the|, not|, rather than|, except|, other than)',
        first_sentence, re.IGNORECASE)
    if match:
        phrase = match.group(1).strip()
        return f'is {phrase}'
    
    # 4. 匹配 "is [answer]" 格式（简单情况）
    match = re.search(
        r'is ([^.;\n]+?)(?=[.;\n]|$)',
        first_sentence, re.IGNORECASE)
    if match:
        phrase = match.group(1).strip()
        if ',' in phrase:
            phrase = phrase.split(',')[0].strip()
        return f'is {phrase}'
    
    # 5. 匹配 "typical of ..." 格式
    match2 = re.search(r'typical of ([a-zA-Z ]+)', first_sentence, re.IGNORECASE)
    if match2:
        return f'typical of {match2.group(1).strip()}'
    
    # 6. 匹配 "called/known as/identified as ..." 格式
    match3 = re.search(r'(called|known as|identified as) ([a-zA-Z ]+)', first_sentence, re.IGNORECASE)
    if match3:
        return f'{match3.group(1)} {match3.group(2).strip()}'
    
    return None

def contains_negative_about_label(text, label, synonyms):
    text = text.lower()
    label = label.lower()
    neg_patterns = [
        fr'not (a |an )?{re.escape(label)}',
        fr'rather than (a |an )?{re.escape(label)}',
        fr'except (a |an )?{re.escape(label)}',
        fr'other than (a |an )?{re.escape(label)}'
    ]
    for pat in neg_patterns:
        if re.search(pat, text):
            return True
    for syn in synonyms:
        for pat in [
            fr'not (a |an )?{re.escape(syn)}',
            fr'rather than (a |an )?{re.escape(syn)}',
            fr'except (a |an )?{re.escape(syn)}',
            fr'other than (a |an )?{re.escape(syn)}'
        ]:
            if re.search(pat, text):
                return True
    return False


def calculate_correctness(answer_text, ann_name_norm, ann_synonyms_norm):
    """
    V-Final: 采用“核心主语优先”原则的正确性判断函数。
    """
    # 1. 获取完整的句子并进行否定检查
    # 我们直接使用 answer_text，因为 extract_is_a_phrase 会处理它
    if contains_negative_about_label(answer_text, ann_name_norm, ann_synonyms_norm):
        return False
        
    # 2. 抽取短语
    is_a_phrase_str = extract_is_a_phrase(answer_text)
    if not is_a_phrase_str:
        return False
    is_a_phrase_norm = normalize_text(is_a_phrase_str)

    # 3. 提取核心主语
    # 我们定义一个简单的规则：核心主语是句首到第一个修饰性介词/连词之间的部分
    core_subject = is_a_phrase_norm
    stoppers = [' wearing ', ' with ', ' holding ', ' containing ', ' filled with', ' on ', ' in ']
    for stop_word in stoppers:
        if stop_word in core_subject:
            core_subject = core_subject.split(stop_word, 1)[0].strip()
            # 移除开头的 "is a" 或 "is an" 或 "is"
            core_subject = re.sub(r'^(is\s+a[n]?|is)\s+', '', core_subject).strip()
            break
    else: # 如果循环正常结束（没有break），说明没有stopper，需要自己清理
        core_subject = re.sub(r'^(is\s+a[n]?|is)\s+', '', core_subject).strip()

    # 4. **分层判断逻辑**
    # Level 1: 检查核心主语是否与标签精确匹配
    if core_subject == ann_name_norm or core_subject in ann_synonyms_norm:
        return True

    # Level 2: 如果核心主语不匹配，则检查整个原始短语是否包含标签。
    # 这一步是为了兼容像 "is a person wearing a necktie" 这样的情况，
    # 即使主语是person，但提到了necktie，也认为是正确的回答。
    if ann_name_norm in is_a_phrase_norm or \
       any(syn_i in is_a_phrase_norm for syn_i in ann_synonyms_norm):
        return True

    # 如果所有检查都失败，则返回 False
    return False



if __name__ == "__main__":
    args = get_args()
    random.seed(42)
    
    # --- 加载文件与数据预处理 ---
    if os.path.isfile(args.pred_file):
        predictions = [json.loads(line) for line in open(args.pred_file)]
    elif os.path.isdir(args.pred_file):
        predictions = [json.loads(line) for pred_file in os.listdir(args.pred_file) for line in open(os.path.join(args.pred_file, pred_file))]
    else:
        raise NotImplementedError('Not supported file format.')
    
    annotations = [json.loads(line) for line in open(args.ann_file)]
    annotations = annotations[:1548] if args.set=='test' else annotations[1548:]
    
    pred_qids = set(pred['question_id'] for pred in predictions)
    filtered_annotations = [anno for anno in annotations if anno['id'] in pred_qids]
    # To maintain order and correct pairing, sort both lists by question_id
    id_to_pred = {pred['question_id']: pred for pred in predictions}
    filtered_predictions = [id_to_pred[anno['id']] for anno in filtered_annotations if anno['id'] in id_to_pred]

    annotations = filtered_annotations
    predictions = filtered_predictions
    print(f"Evaluating {len(predictions)} predictions (matched with annotation)")

    # --- 重构后的评估循环 ---
    baseline_correct_total = 0
    ours_correct_total = 0
    
    baseline_right_ours_wrong = []
    ours_right_baseline_wrong = []

    for pred, ann in tqdm(zip(predictions, annotations), total=len(predictions)):
        # 预处理标注信息
        ann['name'] = ann['name'].replace('_', ' ').strip()
        new_synonyms = []
        for jj in ann['synonyms']:
            if '(' in jj and ')' in jj:
                split_list = jj.split('(')
                if len(split_list) == 2:
                    new_synonyms.append(split_list[0].replace('_', ' ').strip())
                    new_synonyms.append(split_list[1].replace('_', ' ').replace(')', '').strip())
                else:
                    new_synonyms.append(jj.replace('_', ' ').strip())
            else:
                new_synonyms.append(jj.replace('_', ' ').strip())
        ann['synonyms'] = list(set(filter(None, new_synonyms))) # Remove duplicates and empty strings
        
        ann_name_norm = normalize_text(ann['name'])
        ann_synonyms_norm = [normalize_text(s) for s in ann['synonyms']]

        # 分别判断 Baseline 和 Ours 的正确性
        baseline_answer = pred['answers'][0]
        ours_answer = pred['answers'][1]
        
        is_baseline_correct = calculate_correctness(baseline_answer, ann_name_norm, ann_synonyms_norm)
        is_ours_correct = calculate_correctness(ours_answer, ann_name_norm, ann_synonyms_norm)

        if is_baseline_correct:
            baseline_correct_total += 1
        if is_ours_correct:
            ours_correct_total += 1
            
        # 统计切换案例
        if is_baseline_correct and not is_ours_correct:
            baseline_right_ours_wrong.append((pred, ann, extract_is_a_phrase(baseline_answer), extract_is_a_phrase(ours_answer)))
        if is_ours_correct and not is_baseline_correct:
            ours_right_baseline_wrong.append((pred, ann, extract_is_a_phrase(baseline_answer), extract_is_a_phrase(ours_answer)))

    # --- 输出结果 ---
    num_samples = len(predictions) if len(predictions) > 0 else 1
    print(f"Baseline ---> Acc: {baseline_correct_total / num_samples * 100:.3f}%")
    print(f"Ours ---> Acc: {ours_correct_total / num_samples * 100:.3f}%")

    if args.show_baseline_only_miss:
        print(f"\n[原始对但优化错的case，共{len(baseline_right_ours_wrong)}个]")
        for pred, ann, is_a0, is_a1 in baseline_right_ours_wrong:
            print(f"QID: {pred['question_id']} | Label: {ann['name']}")
            print(f"Baseline: {pred['answers'][0]}")
            print(f"  [is_a_phrase]: {is_a0}")
            print(f"Ours: {pred['answers'][1]}\n  [is_a_phrase]: {is_a1}\n")
            
    if args.show_ours_only_hit:
        print(f"\n[优化对但原始错的case，共{len(ours_right_baseline_wrong)}个]")
        for pred, ann, is_a0, is_a1 in ours_right_baseline_wrong:
            print(f"QID: {pred['question_id']} | Label: {ann['name']}")
            print(f"Baseline: {pred['answers'][0]}")
            print(f"  [is_a_phrase]: {is_a0}")
            print(f"Ours: {pred['answers'][1]}\n  [is_a_phrase]: {is_a1}\n")
            
    if args.show_switch_cnt: 
        print(f"\n[统计] 原始对优化错: {len(baseline_right_ours_wrong)} | 优化对原始错: {len(ours_right_baseline_wrong)}")
