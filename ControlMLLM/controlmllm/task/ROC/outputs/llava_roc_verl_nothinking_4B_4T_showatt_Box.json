{"question_id": 163616, "answers": ["USER:  \nIs the object of the image a skirt or a jersey? ASSISTANT: The object of the image is a skirt.", "USER:  \nIs this a skirt (feminine clothing with a waistband) or a jersey (sports clothing with a knit texture)? ASSISTANT: This is a skirt, as it is described as a woman wearing a skirt."], "relevancy": [[0, 0], [0, 0]], "label": "skirt"}
{"question_id": 124071, "answers": ["USER:  \nIs the object of the image a apple or a orange_(fruit)? ASSISTANT: The object of the image is an orange, not an apple.", "USER:  \nIs this an apple (red fruit) or an orange (orange fruit)? ASSISTANT: This is an orange (orange fruit)."], "relevancy": [[0, 0], [0, 0]], "label": "apple"}
{"question_id": 167691, "answers": ["USER:  \nIs the object of the image a napkin or a lettuce? ASSISTANT: The object of the image is a lettuce, not a napkin.", "USER:  \nIs this a napkin (soft, folded paper) or a lettuce (green leafy vegetable)? ASSISTANT: This is a lettuce, not a napkin."], "relevancy": [[0, 0], [0, 0]], "label": "lettuce"}
{"question_id": 222478, "answers": ["USER:  \nIs the object of the image a bottle_cap or a plate? ASSISTANT: The object of the image is a plate.", "USER:  \nIs this a bottle cap (small round object) or a plate (flat dish)? ASSISTANT: This is a bottle cap (small round object) on the table."], "relevancy": [[0, 0], [0, 0]], "label": "bottle_cap"}
{"question_id": 228032, "answers": ["USER:  \nIs the object of the image a mushroom or a sausage? ASSISTANT: The object of the image is a mushroom, not a sausage.", "USER:  \nIs this a mushroom (fungus with a cap and stem) or a sausage (cylindrical food item)? ASSISTANT: This is a pizza with mushrooms and sausages as toppings."], "relevancy": [[0, 0], [0, 0]], "label": "mushroom"}
{"question_id": 116096, "answers": ["USER:  \nIs the object of the image a broccoli or a carrot? ASSISTANT: The object of the image is a carrot.", "USER:  \nIs this a carrot (long orange vegetable) or broccoli (green flower-like vegetable)? ASSISTANT: This is a carrot, not broccoli."], "relevancy": [[0, 0], [0, 0]], "label": "carrot"}
{"question_id": 218633, "answers": ["USER:  \nIs the object of the image a suitcase or a cone? ASSISTANT: The object of the image is a suitcase, not a cone.", "USER:  \nIs this a suitcase (with wheels and a handle) or a cone (shaped like a triangle with a circular base)? ASSISTANT: This is a suitcase, not a cone."], "relevancy": [[0, 0], [0, 0]], "label": "suitcase"}
{"question_id": 240882, "answers": ["USER:  \nIs the object of the image a necktie or a cat? ASSISTANT: The object of the image is a cat, not a necktie.", "USER:  \nIs this a person wearing a necktie, or just a necktie? ASSISTANT: This is a cat wearing a necktie."], "relevancy": [[0, 0], [0, 0]], "label": "necktie"}
{"question_id": 16307, "answers": ["USER:  \nIs the object of the image a person or a cellular_telephone? ASSISTANT: The object of the image is a person, specifically a man, who is holding a cellular telephone.", "USER:  \nIs this a person or a cellular telephone? ASSISTANT: This is a person, specifically a man, who is holding a cellular telephone."], "relevancy": [[0, 0], [0, 0]], "label": "cellular_telephone"}
{"question_id": 76204, "answers": ["USER:  \nIs the object of the image a glove or a baseball_glove? ASSISTANT: The object of the image is a baseball glove.", "USER:  \nIs this a glove or a baseball glove? ASSISTANT: This is a baseball glove, which is being worn by the catcher."], "relevancy": [[0, 0], [0, 0]], "label": "glove"}
{"question_id": 143593, "answers": ["USER:  \nIs the object of the image a life_jacket or a boat? ASSISTANT: The object of the image is a boat.", "USER:  \nIs this a life jacket (waterproof vest) or a boat (watercraft)? ASSISTANT: This is a boat (watercraft) with people on it."], "relevancy": [[0, 0], [0, 0]], "label": "boat"}
{"question_id": 185537, "answers": ["USER:  \nIs the object of the image a beef_(food) or a broccoli? ASSISTANT: The object of the image is a beef (food) dish, which includes meat and potatoes.", "USER:  \nIs this a beef (meat) or broccoli (green vegetable)? ASSISTANT: This is a beef (meat) dish, which includes a steak, potatoes, and broccoli."], "relevancy": [[0, 0], [0, 0]], "label": "broccoli"}
{"question_id": 192023, "answers": ["USER:  \nIs the object of the image a apple or a watermelon? ASSISTANT: The object of the image is a watermelon.", "USER:  \nIs this an apple (red fruit) or a watermelon (green fruit)? ASSISTANT: This is an apple, as it is red and placed on a fruit stand."], "relevancy": [[0, 0], [0, 0]], "label": "watermelon"}
{"question_id": 190178, "answers": ["USER:  \nIs the object of the image a tag or a suitcase? ASSISTANT: The object of the image is a suitcase.", "USER:  \nIs this a tag (small piece of fabric with a label) or a suitcase (large container for carrying belongings)? ASSISTANT: This is a tag, not a suitcase. The tag is attached to a suitcase, which is part of a large pile of lugg"], "relevancy": [[0, 0], [0, 0]], "label": "tag"}
{"question_id": 133214, "answers": ["USER:  \nIs the object of the image a sausage or a mushroom? ASSISTANT: The object of the image is a sausage, not a mushroom.", "USER:  \nIs this a sausage (cylindrical meat product) or a mushroom (fungus with a cap and stem)? ASSISTANT: This is a sausage, not a mushroom."], "relevancy": [[0, 0], [0, 0]], "label": "mushroom"}
{"question_id": 84346, "answers": ["USER:  \nIs the object of the image a street_sign or a car_(automobile)? ASSISTANT: The object of the image is a car (automobile).", "USER:  \nIs this a street sign (with traffic-related information) or a car (a vehicle with a engine and wheels)? ASSISTANT: This is a street sign (with traffic-related information) that is attached to a pole."], "relevancy": [[0, 0], [0, 0]], "label": "street_sign"}
{"question_id": 182087, "answers": ["USER:  \nIs the object of the image a sausage or a chair? ASSISTANT: The object of the image is a sausage, not a chair.", "USER:  \nIs this a sausage (meat product) or a chair (furniture)? ASSISTANT: This is a sausage, not a chair."], "relevancy": [[0, 0], [0, 0]], "label": "chair"}
{"question_id": 40899, "answers": ["USER:  \nIs the object of the image a flag or a knife? ASSISTANT: The object of the image is a knife.", "USER:  \nIs this a flag (with a design and colors) or a knife (metal fastener)? ASSISTANT: This is a knife, not a flag."], "relevancy": [[0, 0], [0, 0]], "label": "flag"}
{"question_id": 141602, "answers": ["USER:  \nIs the object of the image a bottle or a banana? ASSISTANT: The object of the image is a bunch of bananas, not a bottle.", "USER:  \nIs this a bottle (glass container) or a banana (yellow fruit)? ASSISTANT: This is a bottle, not a banana."], "relevancy": [[0, 0], [0, 0]], "label": "banana"}
{"question_id": 87124, "answers": ["USER:  \nIs the object of the image a stove or a cupboard? ASSISTANT: The object of the image is a stove.", "USER:  \nIs this a stove (with a cooking surface) or a cupboard (with doors and shelves)? ASSISTANT: This is a stove with a cooking surface, not a cupboard."], "relevancy": [[0, 0], [0, 0]], "label": "cupboard"}
{"question_id": 21296, "answers": ["USER:  \nIs the object of the image a basket or a knife? ASSISTANT: The object of the image is a knife.", "USER:  \nIs this a basket (woven container) or a knife (metal cutting tool)? ASSISTANT: This is a basket (woven container) with a knife (metal cutting tool) inside it."], "relevancy": [[0, 0], [0, 0]], "label": "knife"}
{"question_id": 161586, "answers": ["USER:  \nIs the object of the image a earring or a wine_bottle? ASSISTANT: The object of the image is a wine bottle.", "USER:  \nIs this a earring (small decorative piece) or a wine_bottle (container for wine)? ASSISTANT: This is a wine bottle, not an earring."], "relevancy": [[0, 0], [0, 0]], "label": "wine_bottle"}
{"question_id": 56004, "answers": ["USER:  \nIs the object of the image a plastic_bag or a telephone? ASSISTANT: The object of the image is a plastic bag.", "USER:  \nIs this a plastic bag (flexible, transparent container) or a telephone (electronic device with a screen and buttons)? ASSISTANT: This is a plastic bag, not a telephone."], "relevancy": [[0, 0], [0, 0]], "label": "plastic_bag"}
{"question_id": 164287, "answers": ["USER:  \nIs the object of the image a suitcase or a backpack? ASSISTANT: The object of the image is a suitcase.", "USER:  \nIs this a suitcase (with wheels and handles) or a backpack (with a top zipper)? ASSISTANT: This is a suitcase with wheels and handles, not a backpack."], "relevancy": [[0, 0], [0, 0]], "label": "backpack"}
{"question_id": 20348, "answers": ["USER:  \nIs the object of the image a blueberry or a raspberry? ASSISTANT: The object of the image is a raspberry.", "USER:  \nIs this a blueberry (small dark purple fruit) or a raspberry (red fruit with fuzzy skin)? ASSISTANT: This is a raspberry, not a blueberry."], "relevancy": [[0, 0], [0, 0]], "label": "raspberry"}
{"question_id": 112278, "answers": ["USER:  \nIs the object of the image a cookie or a tray? ASSISTANT: The object of the image is a tray, which is filled with various types of cookies.", "USER:  \nIs this a cookie (small round baked good) or a tray (flat dish for holding items)? ASSISTANT: This is a tray, not a cookie. The tray is filled with various types of cookies and other baked goods."], "relevancy": [[0, 0], [0, 0]], "label": "tray"}
{"question_id": 35731, "answers": ["USER:  \nIs the object of the image a dress or a necktie? ASSISTANT: The object of the image is a dress.", "USER:  \nIs this a dress (fabric garment) or a necktie (band around the neck)? ASSISTANT: This is a dress (fabric garment) worn by a young girl in the group photo."], "relevancy": [[0, 0], [0, 0]], "label": "dress"}
{"question_id": 131805, "answers": ["USER:  \nIs the object of the image a carrot or a place_mat? ASSISTANT: The object of the image is a carrot, not a place mat.", "USER:  \nIs this a carrot (orange vegetable) or a place_mat (flat surface for placing items)? ASSISTANT: This is a plate of food, which includes a piece of meat and carrots. The plate is placed on a table, and there is a"], "relevancy": [[0, 0], [0, 0]], "label": "carrot"}
{"question_id": 51859, "answers": ["USER:  \nIs the object of the image a traffic_light or a bolt? ASSISTANT: The object of the image is a bolt.", "USER:  \nIs this a traffic light (with colored lights) or a bolt (metal fastener)? ASSISTANT: This is a bolt (metal fastener) on a pole."], "relevancy": [[0, 0], [0, 0]], "label": "bolt"}
{"question_id": 155078, "answers": ["USER:  \nIs the object of the image a vent or a steering_wheel? ASSISTANT: The object of the image is a steering wheel.", "USER:  \nIs this a vent (a hole in a wall or floor) or a steering wheel (a circular control on a vehicle)? ASSISTANT: This is a steering wheel, not a vent."], "relevancy": [[0, 0], [0, 0]], "label": "vent"}
{"question_id": 114684, "answers": ["USER:  \nIs the object of the image a trousers or a sweater? ASSISTANT: The object of the image is a pair of pants.", "USER:  \nIs this trousers (leg covering) or a sweater (upper body clothing)? ASSISTANT: The image shows a man wearing a sweater and pants."], "relevancy": [[0, 0], [0, 0]], "label": "trousers"}
{"question_id": 88345, "answers": ["USER:  \nIs the object of the image a dog_collar or a book? ASSISTANT: The object of the image is a dog collar.", "USER:  \nIs this a dog collar (item worn by a dog) or a book (object with pages)? ASSISTANT: This is a dog collar, not a book."], "relevancy": [[0, 0], [0, 0]], "label": "book"}
{"question_id": 192142, "answers": ["USER:  \nIs the object of the image a wheel or a airplane? ASSISTANT: The object of the image is an airplane, specifically a large United Airlines jet.", "USER:  \nIs this a wheel (circular object) or an airplane (flying vehicle)? ASSISTANT: This is an airplane, not a wheel or an airplane."], "relevancy": [[0, 0], [0, 0]], "label": "wheel"}
{"question_id": 91370, "answers": ["USER:  \nIs the object of the image a water_heater or a cabinet? ASSISTANT: The object of the image is a water heater, which is a white refrigerator with a freezer on top.", "USER:  \nIs this a water heater (with a tank and valves) or a cabinet (a storage unit with doors)? ASSISTANT: This is a water heater, which is a storage unit with doors."], "relevancy": [[0, 0], [0, 0]], "label": "cabinet"}
{"question_id": 62538, "answers": ["USER:  \nIs the object of the image a trousers or a cow? ASSISTANT: The object of the image is a cow, not pants.", "USER:  \nIs this a pair of trousers (clothing item) or a cow (animal)? ASSISTANT: This is a cow, not a pair of pants."], "relevancy": [[0, 0], [0, 0]], "label": "cow"}
{"question_id": 177557, "answers": ["USER:  \nIs the object of the image a pastry or a doughnut? ASSISTANT: The object of the image is a doughnut.", "USER:  \nIs this a pastry (baked good) or a doughnut (circular baked item with a hole)? ASSISTANT: This is a display of various doughnuts, which are circular baked items with a hole."], "relevancy": [[0, 0], [0, 0]], "label": "pastry"}
{"question_id": 192677, "answers": ["USER:  \nIs the object of the image a carrot or a broccoli? ASSISTANT: The object of the image is a broccoli.", "USER:  \nIs this a carrot (long orange vegetable) or broccoli (green flower-like vegetable)? ASSISTANT: This is a carrot (long orange vegetable) and not broccoli (green flower-like vegetable)."], "relevancy": [[0, 0], [0, 0]], "label": "broccoli"}
{"question_id": 108135, "answers": ["USER:  \nIs the object of the image a banner or a baseball_bat? ASSISTANT: The object of the image is a baseball bat.", "USER:  \nIs this a banner (a large piece of fabric) or a baseball bat (a long piece of wood)? ASSISTANT: This is a banner, not a baseball bat."], "relevancy": [[0, 0], [0, 0]], "label": "banner"}
