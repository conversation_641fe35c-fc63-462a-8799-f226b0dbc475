{"question_id": 163616, "answers": ["USER:  \nIs the object of the image a skirt or a jersey? ASSISTANT: The object of the image is a skirt.", "USER:  \nIs the object a skirt or a jersey? ASSISTANT: The object is a skirt."], "relevancy": [[0, 0], [0, 0]], "label": "skirt"}
{"question_id": 124071, "answers": ["USER:  \nIs the object of the image a apple or a orange_(fruit)? ASSISTANT: The object of the image is an orange, not an apple.", "USER:  \nIs the object in the image an apple or an orange? ASSISTANT: The object in the image is an orange."], "relevancy": [[0, 0], [0, 0]], "label": "apple"}
{"question_id": 167691, "answers": ["USER:  \nIs the object of the image a napkin or a lettuce? ASSISTANT: The object of the image is a lettuce, not a napkin.", "USER:  \nIs the object in the image a napkin or a lettuce? ASSISTANT: The object in the image is a lettuce, not a napkin."], "relevancy": [[0, 0], [0, 0]], "label": "lettuce"}
{"question_id": 222478, "answers": ["USER:  \nIs the object of the image a bottle_cap or a plate? ASSISTANT: The object of the image is a plate.", "USER:  \nIs the object in the image a bottle_cap or a plate? ASSISTANT: The object in the image is a bottle cap."], "relevancy": [[0, 0], [0, 0]], "label": "bottle_cap"}
{"question_id": 228032, "answers": ["USER:  \nIs the object of the image a mushroom or a sausage? ASSISTANT: The object of the image is a mushroom, not a sausage.", "USER:  \nIs the object in the image a mushroom or a sausage? ASSISTANT: The object in the image is a mushroom, not a sausage."], "relevancy": [[0, 0], [0, 0]], "label": "mushroom"}
{"question_id": 116096, "answers": ["USER:  \nIs the object of the image a broccoli or a carrot? ASSISTANT: The object of the image is a carrot.", "USER:  \nIs the object in the image a broccoli or a carrot? ASSISTANT: The object in the image is a carrot."], "relevancy": [[0, 0], [0, 0]], "label": "carrot"}
{"question_id": 218633, "answers": ["USER:  \nIs the object of the image a suitcase or a cone? ASSISTANT: The object of the image is a suitcase, not a cone.", "USER:  \nIs the object in the image a suitcase or a cone? ASSISTANT: The object in the image is a suitcase."], "relevancy": [[0, 0], [0, 0]], "label": "suitcase"}
{"question_id": 240882, "answers": ["USER:  \nIs the object of the image a necktie or a cat? ASSISTANT: The object of the image is a cat, not a necktie.", "USER:  \nIs the object a necktie or a cat? ASSISTANT: The object is a cat, not a necktie."], "relevancy": [[0, 0], [0, 0]], "label": "necktie"}
{"question_id": 16307, "answers": ["USER:  \nIs the object of the image a person or a cellular_telephone? ASSISTANT: The object of the image is a person, specifically a man, who is holding a cellular telephone.", "USER:  \nIs the object in the image a person or a cellular_telephone? ASSISTANT: The object in the image is a cellular telephone."], "relevancy": [[0, 0], [0, 0]], "label": "cellular_telephone"}
{"question_id": 76204, "answers": ["USER:  \nIs the object of the image a glove or a baseball_glove? ASSISTANT: The object of the image is a baseball glove.", "USER:  \nIs the object a glove or a baseball_glove? ASSISTANT: The object is a baseball glove."], "relevancy": [[0, 0], [0, 0]], "label": "glove"}
{"question_id": 143593, "answers": ["USER:  \nIs the object of the image a life_jacket or a boat? ASSISTANT: The object of the image is a boat.", "USER:  \nIs the object of the image a life_jacket or a boat? ASSISTANT: The object of the image is a boat."], "relevancy": [[0, 0], [0, 0]], "label": "boat"}
{"question_id": 185537, "answers": ["USER:  \nIs the object of the image a beef_(food) or a broccoli? ASSISTANT: The object of the image is a beef (food) dish, which includes meat and potatoes.", "USER:  \nIs the object in the image a beef or broccoli? ASSISTANT: The object in the image is a piece of beef."], "relevancy": [[0, 0], [0, 0]], "label": "broccoli"}
{"question_id": 192023, "answers": ["USER:  \nIs the object of the image a apple or a watermelon? ASSISTANT: The object of the image is a watermelon.", "USER:  \nIs the object in the image an apple or a watermelon? ASSISTANT: The object in the image is an apple."], "relevancy": [[0, 0], [0, 0]], "label": "watermelon"}
{"question_id": 190178, "answers": ["USER:  \nIs the object of the image a tag or a suitcase? ASSISTANT: The object of the image is a suitcase.", "USER:  \nIs the object in the image a tag or a suitcase? ASSISTANT: The object in the image is a suitcase."], "relevancy": [[0, 0], [0, 0]], "label": "tag"}
{"question_id": 133214, "answers": ["USER:  \nIs the object of the image a sausage or a mushroom? ASSISTANT: The object of the image is a sausage, not a mushroom.", "USER:  \nIs the object in the image a sausage or a mushroom? ASSISTANT: The object in the image is a sausage, not a mushroom."], "relevancy": [[0, 0], [0, 0]], "label": "mushroom"}
{"question_id": 84346, "answers": ["USER:  \nIs the object of the image a street_sign or a car_(automobile)? ASSISTANT: The object of the image is a car (automobile).", "USER:  \nIs the object in the image a street_sign or a car_(automobile)? ASSISTANT: The object in the image is a street sign."], "relevancy": [[0, 0], [0, 0]], "label": "street_sign"}
{"question_id": 182087, "answers": ["USER:  \nIs the object of the image a sausage or a chair? ASSISTANT: The object of the image is a sausage, not a chair.", "USER:  \nIs the object in the image a sausage or a chair? ASSISTANT: The object in the image is a sausage, not a chair."], "relevancy": [[0, 0], [0, 0]], "label": "chair"}
{"question_id": 40899, "answers": ["USER:  \nIs the object of the image a flag or a knife? ASSISTANT: The object of the image is a knife.", "USER:  \nIs the object in the image a flag or a knife? ASSISTANT: The object in the image is a knife."], "relevancy": [[0, 0], [0, 0]], "label": "flag"}
{"question_id": 141602, "answers": ["USER:  \nIs the object of the image a bottle or a banana? ASSISTANT: The object of the image is a bunch of bananas, not a bottle.", "USER:  \nIs the object in the image a bottle or a banana? ASSISTANT: The object in the image is a banana."], "relevancy": [[0, 0], [0, 0]], "label": "banana"}
{"question_id": 87124, "answers": ["USER:  \nIs the object of the image a stove or a cupboard? ASSISTANT: The object of the image is a stove.", "USER:  \nIs the object in the image a stove or a cupboard? ASSISTANT: The object in the image is a stove."], "relevancy": [[0, 0], [0, 0]], "label": "cupboard"}
{"question_id": 21296, "answers": ["USER:  \nIs the object of the image a basket or a knife? ASSISTANT: The object of the image is a knife.", "USER:  \nIs the object in the image a basket or a knife? ASSISTANT: The object in the image is a knife."], "relevancy": [[0, 0], [0, 0]], "label": "knife"}
{"question_id": 161586, "answers": ["USER:  \nIs the object of the image a earring or a wine_bottle? ASSISTANT: The object of the image is a wine bottle.", "USER:  \nIs the object in the image an earring or a wine_bottle? ASSISTANT: The object in the image is a wine bottle."], "relevancy": [[0, 0], [0, 0]], "label": "wine_bottle"}
{"question_id": 56004, "answers": ["USER:  \nIs the object of the image a plastic_bag or a telephone? ASSISTANT: The object of the image is a plastic bag.", "USER:  \nIs the object in the image a plastic_bag or a telephone? ASSISTANT: The object in the image is a plastic bag."], "relevancy": [[0, 0], [0, 0]], "label": "plastic_bag"}
{"question_id": 164287, "answers": ["USER:  \nIs the object of the image a suitcase or a backpack? ASSISTANT: The object of the image is a suitcase.", "USER:  \nIs the object in the image a suitcase or a backpack? ASSISTANT: The object in the image is a suitcase."], "relevancy": [[0, 0], [0, 0]], "label": "backpack"}
{"question_id": 20348, "answers": ["USER:  \nIs the object of the image a blueberry or a raspberry? ASSISTANT: The object of the image is a raspberry.", "USER:  \nIs the object in the image a blueberry or a raspberry? ASSISTANT: The object in the image is a raspberry."], "relevancy": [[0, 0], [0, 0]], "label": "raspberry"}
{"question_id": 112278, "answers": ["USER:  \nIs the object of the image a cookie or a tray? ASSISTANT: The object of the image is a tray, which is filled with various types of cookies.", "USER:  \nIs the object in the image a cookie or a tray? ASSISTANT: The object in the image is a tray, which is filled with various types of cookies."], "relevancy": [[0, 0], [0, 0]], "label": "tray"}
{"question_id": 35731, "answers": ["USER:  \nIs the object of the image a dress or a necktie? ASSISTANT: The object of the image is a dress.", "USER:  \nIs the object a dress or a necktie? ASSISTANT: The object in the image is a necktie."], "relevancy": [[0, 0], [0, 0]], "label": "dress"}
{"question_id": 131805, "answers": ["USER:  \nIs the object of the image a carrot or a place_mat? ASSISTANT: The object of the image is a carrot, not a place mat.", "USER:  \nIs the object in the image a carrot or a place_mat? ASSISTANT: The object in the image is a carrot, not a place mat."], "relevancy": [[0, 0], [0, 0]], "label": "carrot"}
{"question_id": 51859, "answers": ["USER:  \nIs the object of the image a traffic_light or a bolt? ASSISTANT: The object of the image is a bolt.", "USER:  \nIs the object in the image a traffic_light or a bolt? ASSISTANT: The object in the image is a bolt."], "relevancy": [[0, 0], [0, 0]], "label": "bolt"}
{"question_id": 155078, "answers": ["USER:  \nIs the object of the image a vent or a steering_wheel? ASSISTANT: The object of the image is a steering wheel.", "USER:  \nIs the object in the image a vent or a steering_wheel? ASSISTANT: The object in the image is a steering wheel."], "relevancy": [[0, 0], [0, 0]], "label": "vent"}
{"question_id": 114684, "answers": ["USER:  \nIs the object of the image a trousers or a sweater? ASSISTANT: The object of the image is a pair of pants.", "USER:  \nIs the object in the image a trousers or a sweater? ASSISTANT: The object in the image is a pair of pants."], "relevancy": [[0, 0], [0, 0]], "label": "trousers"}
{"question_id": 88345, "answers": ["USER:  \nIs the object of the image a dog_collar or a book? ASSISTANT: The object of the image is a dog collar.", "USER:  \nIs the object in the image a dog collar or a book? ASSISTANT: The object in the image is a dog collar."], "relevancy": [[0, 0], [0, 0]], "label": "book"}
{"question_id": 192142, "answers": ["USER:  \nIs the object of the image a wheel or a airplane? ASSISTANT: The object of the image is an airplane, specifically a large United Airlines jet.", "USER:  \nIs the object in the image a wheel or an airplane? ASSISTANT: The object in the image is an airplane, specifically a large United Airlines jet."], "relevancy": [[0, 0], [0, 0]], "label": "wheel"}
{"question_id": 91370, "answers": ["USER:  \nIs the object of the image a water_heater or a cabinet? ASSISTANT: The object of the image is a water heater, which is a white refrigerator with a freezer on top.", "USER:  \nIs the object in the image a water_heater or a cabinet? ASSISTANT: The object in the image is a water heater."], "relevancy": [[0, 0], [0, 0]], "label": "cabinet"}
{"question_id": 62538, "answers": ["USER:  \nIs the object of the image a trousers or a cow? ASSISTANT: The object of the image is a cow, not pants.", "USER:  \nIs the object of the image a trousers or a cow? ASSISTANT: The object of the image is a cow, not pants."], "relevancy": [[0, 0], [0, 0]], "label": "cow"}
{"question_id": 177557, "answers": ["USER:  \nIs the object of the image a pastry or a doughnut? ASSISTANT: The object of the image is a doughnut.", "USER:  \nIs the object in the image a pastry or a doughnut? ASSISTANT: The object in the image is a doughnut."], "relevancy": [[0, 0], [0, 0]], "label": "pastry"}
{"question_id": 192677, "answers": ["USER:  \nIs the object of the image a carrot or a broccoli? ASSISTANT: The object of the image is a broccoli.", "USER:  \nIs the object in the image a carrot or broccoli? ASSISTANT: The object in the image is a carrot."], "relevancy": [[0, 0], [0, 0]], "label": "broccoli"}
{"question_id": 108135, "answers": ["USER:  \nIs the object of the image a banner or a baseball_bat? ASSISTANT: The object of the image is a baseball bat.", "USER:  \nIs the object in the image a banner or a baseball bat? ASSISTANT: The object in the image is a baseball bat."], "relevancy": [[0, 0], [0, 0]], "label": "banner"}
{"question_id": 84612, "answers": ["USER:  \nIs the object of the image a tray or a muffin? ASSISTANT: The object of the image is a tray, which is filled with muffins.", "USER:  \nIs the object in the image a tray or a muffin? ASSISTANT: The object in the image is a tray, which is filled with muffins."], "relevancy": [[0, 0], [0, 0]], "label": "muffin"}
{"question_id": 236117, "answers": ["USER:  \nIs the object of the image a freight_car or a railcar_(part_of_a_train)? ASSISTANT: The object of the image is a railcar, which is part of a train.", "USER:  \nIs the object a freight_car or a railcar_(part_of_a_train)? ASSISTANT: The object is a railcar, which is part of a train."], "relevancy": [[0, 0], [0, 0]], "label": "freight_car"}
{"question_id": 42241, "answers": ["USER:  \nIs the object of the image a soup or a bowl? ASSISTANT: The object of the image is a bowl, which is filled with noodles.", "USER:  \nIs the object in the image a soup or a bowl? ASSISTANT: The object in the image is a bowl."], "relevancy": [[0, 0], [0, 0]], "label": "bowl"}
{"question_id": 40304, "answers": ["USER:  \nIs the object of the image a cow or a pole? ASSISTANT: The object of the image is a cow.", "USER:  \nIs the object in the image a cow or a pole? ASSISTANT: The object in the image is a cow."], "relevancy": [[0, 0], [0, 0]], "label": "cow"}
{"question_id": 46647, "answers": ["USER:  \nIs the object of the image a basket or a carrot? ASSISTANT: The object of the image is a basket filled with carrots.", "USER:  \nIs the object in the image a basket or a carrot? ASSISTANT: The object in the image is a basket filled with carrots."], "relevancy": [[0, 0], [0, 0]], "label": "carrot"}
{"question_id": 51826, "answers": ["USER:  \nIs the object of the image a necktie or a person? ASSISTANT: The object of the image is a person wearing a necktie.", "USER:  \nIs the object a necktie or a person? ASSISTANT: The object is a necktie, not a person."], "relevancy": [[0, 0], [0, 0]], "label": "necktie"}
{"question_id": 10657, "answers": ["USER:  \nIs the object of the image a pillow or a magazine? ASSISTANT: The object of the image is a pillow.", "USER:  \nIs the object in the image a pillow or a magazine? ASSISTANT: The object in the image is a pillow."], "relevancy": [[0, 0], [0, 0]], "label": "magazine"}
{"question_id": 185823, "answers": ["USER:  \nIs the object of the image a raft or a swimsuit? ASSISTANT: The object of the image is a swimsuit, not a raft.", "USER:  \nDoes the object contain or be worn by another object? ASSISTANT: Yes, the object is a life jacket, which is worn by a person in the water."], "relevancy": [[0, 0], [0, 0]], "label": "swimsuit"}
