# Visual Anchor Embeddings - 快速入门指南

## 🚀 5分钟快速开始

### 第一步：检查环境

确保你已经有：
- ✅ LLaVA-1.5-7B模型
- ✅ ROC数据集
- ✅ 基本的ControlMLLM环境

### 第二步：一键运行

```bash
cd ControlMLLM
chmod +x run_visual_anchors.sh
./run_visual_anchors.sh
```

脚本会自动：
- 📊 运行基本配置实验 (spatial=2, semantic=2, relational=2)
- 🔬 询问是否运行消融实验
- 💾 保存结果到 `outputs/` 目录

### 第三步：查看结果

```bash
# 分析实验结果
python analyze_visual_anchors.py --results_dir outputs

# 查看生成的报告
ls analysis/
# → performance_comparison.png
# → relationship_distribution.png  
# → analysis_report.md
```

## 📋 核心参数速查

### 关键参数说明

| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `--T` | 15 | 优化步数，通常10-20步足够 |
| `--lr` | 0.02 | 学习率，Box/Scribble用0.02，Mask用0.015 |
| `--alpha` | 400 | Loss权重，控制attention loss的强度 |
| `--n_spatial_anchors` | 2 | 空间anchor数量，学习"在哪里看" |
| `--n_semantic_anchors` | 2 | 语义anchor数量，学习"看什么特征" |
| `--n_relational_anchors` | 2 | 关系anchor数量，学习"如何比较" |

### 不同Visual Prompt的建议配置

```bash
# Box prompt (推荐)
python llava_roc_visual_anchors.py --visual_prompt Box --lr 0.02 --T 15 \
    --n_spatial_anchors 2 --n_semantic_anchors 2 --n_relational_anchors 2

# Point prompt
python llava_roc_visual_anchors.py --visual_prompt Point --lr 0.025 --T 12 \
    --n_spatial_anchors 3 --n_semantic_anchors 2 --n_relational_anchors 1

# Mask prompt  
python llava_roc_visual_anchors.py --visual_prompt Mask --lr 0.015 --T 20 \
    --n_spatial_anchors 3 --n_semantic_anchors 1 --n_relational_anchors 2
```

## 🎯 预期看到的效果

### 正常的训练输出

```
Processing question 123: Is this a person or a necktie?
Original question: Is this a person or a necktie?
Objects: person vs necktie, Relationship: containment
Anchor configuration: {'spatial': '2 anchors for spatial grounding', 'semantic': '2 anchors for semantic understanding', 'relational': '2 anchors for relational comparison'}
Original output: USER:  Is this a person or a necktie? ASSISTANT: This is a person.
Step 0: Loss = 2.3456
Step 1: Loss = 2.1234
Step 2: Loss = 1.9876
...
Step 14: Loss = 0.8765
Optimized output: USER:  Is this a person or a necktie? ASSISTANT: This is a person wearing a necktie.
Best loss: 0.8765
```

### 成功的指标

- ✅ **Loss下降**: 从 ~2.0 降到 <1.0
- ✅ **输出改进**: 答案更精确，包含更多细节
- ✅ **稳定收敛**: 不出现loss震荡或发散
- ✅ **关系识别**: 正确识别对象间的关系类型

## 🛠️ 故障排除

### 常见问题

**Q1: Loss不下降或震荡**
```bash
# 解决方案：降低学习率
--lr 0.01  # 从0.02降到0.01
```

**Q2: 内存不足**
```bash
# 解决方案：减少anchor数量
--n_spatial_anchors 1 --n_semantic_anchors 1 --n_relational_anchors 1
```

**Q3: 优化太慢**
```bash
# 解决方案：启用早停
--early_stop --loss_threshold 0.001
```

**Q4: 找不到attention maps**
```bash
# 解决方案：检查eager attention设置
# 确保代码中有 force_eager_attention_with_decorator(model)
```

### 调试技巧

```bash
# 详细输出模式
python llava_roc_visual_anchors.py --verbose

# 可视化attention maps  
python llava_roc_visual_anchors.py --show_att

# 测试单个问题
python llava_roc_visual_anchors.py --question_file single_question.json
```

## 📈 进阶优化

### 1. 针对特定关系类型优化

```python
# 编辑 analyze_object_relationship 函数添加新的关系类型
containment_pairs = [
    ('person', 'necktie'), ('person', 'helmet'),
    ('your_container', 'your_content')  # 添加你的数据
]
```

### 2. 自定义anchor初始化

```python
# 在 VisualAnchorOptimizer.__init__ 中
if relationship_type == 'containment':
    # 为containment关系增强relational anchors
    self.relational_anchors = nn.Parameter(
        torch.randn(n_relational, embed_dim) * init_std * 1.5
    )
```

### 3. 动态学习率调整

```bash
# 可以添加学习率调度
--lr 0.02  # 初始学习率
# 在代码中添加：lr = lr * 0.9 每5步
```

## 🎪 实验建议

### 消融实验顺序

1. **Baseline**: 运行原始LLaVA (无优化)
2. **Full**: 完整anchor配置
3. **Spatial only**: 测试空间anchor的贡献
4. **Semantic only**: 测试语义anchor的贡献  
5. **Relational only**: 测试关系anchor的贡献

### 超参数扫描

```bash
# 学习率扫描
for lr in 0.01 0.015 0.02 0.025 0.03; do
    python llava_roc_visual_anchors.py --lr $lr --answers_file "outputs/lr_${lr}.json"
done

# Steps扫描
for T in 10 15 20 25; do
    python llava_roc_visual_anchors.py --T $T --answers_file "outputs/T_${T}.json" 
done
```

## 📞 获取帮助

如果遇到问题：

1. **检查日志**: 查看终端输出的详细错误信息
2. **查看配置**: 确认 `config_visual_anchors.yaml` 设置正确
3. **验证数据**: 确保数据路径和格式正确
4. **内存监控**: 使用 `nvidia-smi` 检查GPU内存使用

## 🏃‍♂️ 下一步

成功运行基本实验后，你可以：

1. 📊 **分析结果**: 使用分析脚本深入了解性能
2. 🔬 **消融实验**: 理解每种anchor的贡献
3. 🎯 **优化配置**: 根据你的数据调整参数
4. 🚀 **扩展方法**: 尝试新的anchor类型或优化策略

---

**快速开始完成！** 🎉

现在你已经掌握了Visual Anchor Embeddings的基本用法。更多高级功能请参考完整的 `Visual_Anchor_Embeddings_README.md`。 